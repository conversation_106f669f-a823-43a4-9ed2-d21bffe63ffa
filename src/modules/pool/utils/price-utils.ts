/**
 * Utility functions for converting TGE prices to Uniswap V3 sqrtPriceX96 format
 */

/**
 * Sort token addresses and determine ordering
 * @param usdcAddress - USDC token address
 * @param projectTokenAddress - Project token address
 * @returns Sorted addresses and USDC position
 */
export function sortTokenAddresses(usdcAddress: string, projectTokenAddress: string) {
  const isUsdcToken0 = usdcAddress.toLowerCase() < projectTokenAddress.toLowerCase()

  return {
    token0: isUsdcToken0 ? usdcAddress : projectTokenAddress,
    token1: isUsdcToken0 ? projectTokenAddress : usdcAddress,
    isUsdcToken0,
  }
}

/**
 * Convert TGE price to sqrtPriceX96 format used by Uniswap V3
 * @param tgePrice - The TGE price in USDC (e.g., 1.5 means 1 PROJECT_TOKEN = 1.5 USDC)
 * @param usdcAddress - USDC token address
 * @param projectTokenAddress - Project token address
 * @returns sqrtPriceX96 as string
 */
export function tgePriceToSqrtPriceX96(tgePrice: number, usdcAddress: string, projectTokenAddress: string): string {
  if (tgePrice <= 0) {
    throw new Error('TGE price must be positive')
  }

  // Sort token addresses to determine token0 and token1
  const { isUsdcToken0 } = sortTokenAddresses(usdcAddress, projectTokenAddress)

  // Determine the price ratio based on token ordering
  // Uniswap V3 price is always token1/token0
  let price: number

  if (isUsdcToken0) {
    // USDC is token0, project token is token1
    // Price = PROJECT_TOKEN/USDC = 1/tgePrice
    price = 1 / tgePrice
  } else {
    // Project token is token0, USDC is token1
    // Price = USDC/PROJECT_TOKEN = tgePrice
    price = tgePrice
  }

  // Calculate sqrt(price) * 2^96
  const sqrtPrice = Math.sqrt(price)
  const Q96 = 2n ** 96n

  // Convert to BigInt for precision
  const sqrtPriceX96 = BigInt(Math.floor(sqrtPrice * Number(Q96)))

  return sqrtPriceX96.toString()
}

/**
 * Validate TGE price input
 * @param tgePrice - The TGE price to validate
 * @throws Error if price is invalid
 */
export function validateTgePrice(tgePrice: number): void {
  if (!Number.isFinite(tgePrice)) {
    throw new Error('TGE price must be a valid number')
  }

  if (tgePrice <= 0) {
    throw new Error('TGE price must be positive')
  }

  if (tgePrice > 1000000) {
    throw new Error('TGE price is too large (max: 1,000,000)')
  }
}

/**
 * Convert sqrtPriceX96 back to human-readable price (for testing/debugging)
 * @param sqrtPriceX96 - The sqrtPriceX96 value as string
 * @returns The price as number
 */
export function sqrtPriceX96ToPrice(sqrtPriceX96: string): number {
  const Q96 = 2n ** 96n
  const sqrtPriceBig = BigInt(sqrtPriceX96)
  const sqrtPrice = Number(sqrtPriceBig) / Number(Q96)

  return sqrtPrice ** 2
}

/**
 * Get common TGE price examples for documentation
 */
export const TGE_PRICE_EXAMPLES = {
  '0.1': 'One token costs $0.10',
  '0.5': 'One token costs $0.50',
  '1.0': 'One token costs $1.00',
  '1.5': 'One token costs $1.50',
  '10.0': 'One token costs $10.00',
} as const
