import { Module, forwardRef } from '@nestjs/common'

import { IndexerApiClient } from './clients/indexer-api.client'
import { PoolController } from './pool.controller'
import { PoolService } from './pool.service'
import { DatabaseModule } from '../../libs/database/database.module'
import { BlockchainModule } from '../blockchain/blockchain.module'
import { ProjectsModule } from '../projects/projects.module'

@Module({
  imports: [BlockchainModule, DatabaseModule, forwardRef(() => ProjectsModule)],
  controllers: [PoolController],
  providers: [PoolService, IndexerApiClient],
  exports: [PoolService],
})
export class PoolModule {}
