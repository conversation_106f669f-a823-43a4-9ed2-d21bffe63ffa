import { Injectable, Logger } from '@nestjs/common'
import axios, { AxiosInstance, AxiosError } from 'axios'
import { z } from 'zod'

import { getEnvConfig } from '@/config/env'
import { getErrorMessage } from '@/utils/get-error-message'

// Custom error types for better error handling
export class IndexerApiError extends Error {
  constructor(
    message: string,
    public readonly statusCode?: number,
    public readonly endpoint?: string,
  ) {
    super(message)
    this.name = 'IndexerApiError'
  }
}

export class IndexerApiValidationError extends Error {
  constructor(
    message: string,
    public readonly endpoint?: string,
  ) {
    super(message)
    this.name = 'IndexerApiValidationError'
  }
}

export class IndexerApiNetworkError extends Error {
  constructor(
    message: string,
    public readonly endpoint?: string,
  ) {
    super(message)
    this.name = 'IndexerApiNetworkError'
  }
}

// Response schemas for validation
const totalPoolLiquidityResponseSchema = z.string()

const totalPmLiquidityResponseSchema = z.string()

const tokenDetailsResponseSchema = z.object({
  token_address: z.string(),
  decimals: z.string(),
  name: z.string().nullable(),
})

// Types
export interface TokenDetails {
  tokenAddress: string
  decimals: string
  name: string | null
}

export interface PoolLiquidityOptions {
  pmLiquidityOnly?: boolean
}

@Injectable()
export class IndexerApiClient {
  private readonly logger = new Logger(IndexerApiClient.name)
  private readonly client: AxiosInstance

  constructor() {
    const envConfig = getEnvConfig()
    this.client = axios.create({
      baseURL: envConfig.indexer.apiUrl,
      timeout: 10000, // 10 second timeout
      headers: {
        'Content-Type': 'application/json',
      },
    })

    // Add request/response interceptors for logging
    this.client.interceptors.request.use(
      (config) => {
        this.logger.debug(`Making request to ${config.method?.toUpperCase()} ${config.url}`)

        return config
      },
      (error) => {
        this.logger.error(`Request error: ${getErrorMessage(error)}`)

        return Promise.reject(error)
      },
    )

    this.client.interceptors.response.use(
      (response) => {
        this.logger.debug(`Response received from ${response.config.url} with status ${response.status}`)

        return response
      },
      (error) => {
        this.logger.error(`Response error: ${getErrorMessage(error)}`)

        return Promise.reject(error)
      },
    )
  }

  /**
   * Handle and classify errors from API calls
   * @param error The error to handle
   * @param endpoint The endpoint that failed
   * @returns A properly classified error
   */
  private handleApiError(error: unknown, endpoint: string): Error {
    if (axios.isAxiosError(error)) {
      const axiosError = error as AxiosError

      // Network or connection errors
      if (!axiosError.response) {
        return new IndexerApiNetworkError(`Network error when calling ${endpoint}: ${axiosError.message}`, endpoint)
      }

      // HTTP error responses
      const statusCode = axiosError.response.status
      const { statusText } = axiosError.response

      if (statusCode >= 500) {
        return new IndexerApiError(
          `Server error (${statusCode}) when calling ${endpoint}: ${statusText}`,
          statusCode,
          endpoint,
        )
      }

      if (statusCode === 404) {
        return new IndexerApiError(`Resource not found when calling ${endpoint}`, statusCode, endpoint)
      }

      if (statusCode >= 400) {
        return new IndexerApiError(
          `Client error (${statusCode}) when calling ${endpoint}: ${statusText}`,
          statusCode,
          endpoint,
        )
      }
    }

    // Validation errors (from zod)
    if (error instanceof z.ZodError) {
      return new IndexerApiValidationError(`Invalid response format from ${endpoint}: ${error.message}`, endpoint)
    }

    // Generic error fallback
    return new IndexerApiError(`Unknown error when calling ${endpoint}: ${getErrorMessage(error)}`, undefined, endpoint)
  }

  /**
   * Get total pool liquidity for a specific pool address
   * @param poolAddress The pool address to query
   * @param options Optional parameters
   * @returns String representation of total liquidity
   */
  async getTotalPoolLiquidity(poolAddress: string, options?: PoolLiquidityOptions): Promise<string> {
    const endpoint = `/total_pool_liquidity/${poolAddress}`

    try {
      this.logger.log(`Getting total pool liquidity for pool: ${poolAddress}`)

      const params = options?.pmLiquidityOnly ? { pm_liquidity_only: true } : {}

      const response = await this.client.get(endpoint, { params })

      // Validate response
      const validatedData = totalPoolLiquidityResponseSchema.parse(response.data)

      this.logger.log(`Successfully retrieved pool liquidity: ${validatedData}`)

      return validatedData
    } catch (error) {
      const handledError = this.handleApiError(error, endpoint)
      this.logger.error(`Failed to get total pool liquidity for ${poolAddress}: ${handledError.message}`)
      throw handledError
    }
  }

  /**
   * Get total PM (Project Manager) liquidity across all pools
   * @returns String representation of total PM liquidity
   */
  async getTotalPmLiquidity(): Promise<string> {
    const endpoint = '/total_pm_liquidity'

    try {
      this.logger.log('Getting total PM liquidity')

      const response = await this.client.get(endpoint)

      // Validate response
      const validatedData = totalPmLiquidityResponseSchema.parse(response.data)

      this.logger.log(`Successfully retrieved total PM liquidity: ${validatedData}`)

      return validatedData
    } catch (error) {
      const handledError = this.handleApiError(error, endpoint)
      this.logger.error(`Failed to get total PM liquidity: ${handledError.message}`)
      throw handledError
    }
  }

  /**
   * Get token details by token address and chain ID
   * @param tokenAddress The token contract address
   * @param chainId The chain ID
   * @returns Token details including address, decimals, and name
   */
  async getTokenDetails(tokenAddress: string, chainId: string): Promise<TokenDetails> {
    const endpoint = `/tokens/${tokenAddress}/${chainId}`

    try {
      this.logger.log(`Getting token details for ${tokenAddress} on chain ${chainId}`)

      const response = await this.client.get(endpoint)

      // Validate response
      const validatedData = tokenDetailsResponseSchema.parse(response.data)

      const tokenDetails: TokenDetails = {
        tokenAddress: validatedData.token_address,
        decimals: validatedData.decimals,
        name: validatedData.name,
      }

      this.logger.log(`Successfully retrieved token details for ${tokenAddress}`)

      return tokenDetails
    } catch (error) {
      const handledError = this.handleApiError(error, endpoint)
      this.logger.error(`Failed to get token details for ${tokenAddress} on chain ${chainId}: ${handledError.message}`)
      throw handledError
    }
  }

  /**
   * Check if a pool has liquidity by checking if total liquidity is greater than zero
   * @param poolAddress The pool address to check
   * @returns Boolean indicating if pool has liquidity
   */
  async hasPoolLiquidity(poolAddress: string): Promise<boolean> {
    try {
      const totalLiquidity = await this.getTotalPoolLiquidity(poolAddress)
      const liquidityAmount = BigInt(totalLiquidity)

      return liquidityAmount > 0n
    } catch (error) {
      // Log the specific error type for better debugging
      if (error instanceof IndexerApiError) {
        this.logger.warn(`API error checking pool liquidity for ${poolAddress} (${error.statusCode}): ${error.message}`)
      } else if (error instanceof IndexerApiNetworkError) {
        this.logger.warn(`Network error checking pool liquidity for ${poolAddress}: ${error.message}`)
      } else {
        this.logger.error(`Failed to check pool liquidity for ${poolAddress}: ${getErrorMessage(error)}`)
      }

      // Return false as a safe fallback - no liquidity assumed if we can't check
      return false
    }
  }

  /**
   * Check if a specific pool has PM (Project Manager) liquidity
   * @param poolAddress The pool address to check
   * @returns Boolean indicating if this specific pool has PM liquidity
   */
  async hasPoolPmLiquidity(poolAddress: string): Promise<boolean> {
    try {
      const pmLiquidity = await this.getTotalPoolLiquidity(poolAddress, { pmLiquidityOnly: true })
      const liquidityAmount = BigInt(pmLiquidity)

      return liquidityAmount > 0n
    } catch (error) {
      // Log the specific error type for better debugging
      if (error instanceof IndexerApiError) {
        this.logger.warn(`API error checking PM liquidity for ${poolAddress} (${error.statusCode}): ${error.message}`)
      } else if (error instanceof IndexerApiNetworkError) {
        this.logger.warn(`Network error checking PM liquidity for ${poolAddress}: ${error.message}`)
      } else {
        this.logger.error(`Failed to check PM liquidity for ${poolAddress}: ${getErrorMessage(error)}`)
      }

      // Return false as a safe fallback - no liquidity assumed if we can't check
      return false
    }
  }
}
