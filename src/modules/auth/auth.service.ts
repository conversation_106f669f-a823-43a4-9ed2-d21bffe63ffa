import { Injectable, UnauthorizedException, BadRequestException, Logger, Inject, OnModuleInit } from '@nestjs/common'
import { betterAuth } from 'better-auth'
import { drizzleAdapter } from 'better-auth/adapters/drizzle'
import { emailOTP } from 'better-auth/plugins'
import { eq, and } from 'drizzle-orm'
import { NodePgDatabase } from 'drizzle-orm/node-postgres'
import { Request, Response } from 'express'
import { nanoid } from 'nanoid'

import { EmailService } from './services/email.service'
import { SmsService } from './services/sms.service'
import * as schema from '../../libs/database/schema'
import { users, sessions, verifications, accounts } from '../../libs/database/schema/index.js'

// Time constants
const SESSION_DURATION_MS = 7 * 24 * 60 * 60 * 1000 // 7 days in milliseconds
const OTP_EXPIRY_MS = 5 * 60 * 1000 // 5 minutes in milliseconds

// Time constants in seconds (for Better Auth configuration)
const SESSION_DURATION_SECONDS = 60 * 60 * 24 * 7 // 7 days
const SESSION_UPDATE_AGE_SECONDS = 60 * 60 * 24 // 1 day
const COOKIE_CACHE_MAX_AGE_SECONDS = 5 * 60 // 5 minutes
const OTP_EXPIRY_SECONDS = 300 // 5 minutes

// Cookie name constants
const SESSION_COOKIE_NAME = 'caishen-pro.session_token'
const SECURE_SESSION_COOKIE_NAME = '__Secure-caishen-pro.session_token'

@Injectable()
export class AuthService implements OnModuleInit {
  private readonly logger = new Logger(AuthService.name)
  private betterAuthInstance: any

  constructor(
    @Inject('DB') private db: NodePgDatabase<typeof schema>,
    private emailService: EmailService,
    private smsService: SmsService,
  ) {}

  async onModuleInit() {
    this.logger.log('Initializing Better Auth...')

    // Environment-based configuration
    const isProduction = process.env.NODE_ENV === 'production'
    const isDevelopment = !isProduction

    this.logger.log(`Environment: ${isProduction ? 'production' : 'development'}`)

    // Initialize Better Auth with the injected database connection
    this.betterAuthInstance = betterAuth({
      appName: 'auth-api',
      basePath: '/auth',
      trustedOrigins: [
        'https://caishen-pro-launch.vercel.app',
        'https://launch.caishen.io', // Production frontend
        // Add development origins
        ...(isDevelopment ? ['http://localhost:3000', 'http://localhost:3001'] : []),
      ],
      database: drizzleAdapter(this.db, {
        provider: 'pg',
        schema: {
          user: users,
          session: sessions,
          account: accounts,
          verification: verifications,
        },
      }),
      emailAndPassword: {
        enabled: false, // Disable password authentication
      },
      session: {
        expiresIn: SESSION_DURATION_SECONDS, // 7 days
        updateAge: SESSION_UPDATE_AGE_SECONDS, // 1 day (session refresh)
        cookieCache: {
          enabled: true,
          maxAge: COOKIE_CACHE_MAX_AGE_SECONDS, // 5 minutes cache
        },
      },
      // Better Auth cookie configuration - environment specific
      advanced: {
        cookiePrefix: 'caishen-pro', // This will create cookies like "caishen-pro.session_token"
        // Enable cross-subdomain cookies only in production
        ...(isProduction
          ? {
              crossSubDomainCookies: {
                enabled: true,
                domain: '.caishen.io', // Share cookies across all caishen.io subdomains
              },
            }
          : {}),
        defaultCookieAttributes: {
          secure: isProduction, // Secure only in production (HTTPS)
          sameSite: isProduction ? 'lax' : 'none', // 'lax' for production subdomains, 'none' for dev cross-origin
          httpOnly: true,
          path: '/',
          // Add partitioned for development cross-origin
          ...(isDevelopment ? { partitioned: true } : {}),
        },
        useSecureCookies: isProduction, // Force secure cookies only in production
        // Additional Safari compatibility settings
        generateId: () => nanoid(32), // Ensure consistent ID generation
      },
      user: {
        additionalFields: {
          phoneNumber: {
            type: 'string',
            required: false,
          },
          phoneVerified: {
            type: 'boolean',
            defaultValue: false,
          },
        },
      },
      plugins: [
        emailOTP({
          sendVerificationOTP: async ({ email, otp, type }) => {
            try {
              // Use the injected email service
              const emailType =
                type === 'forget-password' ? 'password-reset' : (type as 'sign-in' | 'email-verification')
              await this.emailService.sendOTP(email, otp, emailType)
              this.logger.log(`[Better Auth] Successfully sent OTP email to ${email}`)
            } catch (error) {
              this.logger.error(`[Better Auth] Failed to send OTP email to ${email}:`, error)
              throw error
            }
          },
          otpLength: 6,
          expiresIn: OTP_EXPIRY_SECONDS, // 5 minutes
          sendVerificationOnSignUp: false,
          disableSignUp: false,
        }),
      ],
    })

    this.logger.log('Better Auth initialized successfully')
  }

  // Getter to access the Better Auth instance
  get auth() {
    if (!this.betterAuthInstance) {
      throw new Error('Better Auth not initialized. Make sure onModuleInit has been called.')
    }

    return this.betterAuthInstance
  }

  /**
   * Get session using our custom session management with Better Auth cookie format
   */
  async getBetterAuthSession(request: Request) {
    try {
      // Get session token from cookies or auth header
      const sessionToken = this.extractSessionTokenFromRequest(request)

      if (!sessionToken) {
        return null
      }

      // Look up session in our database
      const sessionResult = await this.db
        .select()
        .from(sessions)
        .leftJoin(users, eq(sessions.userId, users.id))
        .where(eq(sessions.token, sessionToken))
        .limit(1)

      if (sessionResult.length === 0 || !sessionResult[0].session || !sessionResult[0].user) {
        return null
      }

      const { session } = sessionResult[0]
      const { user } = sessionResult[0]

      if (session.expiresAt < new Date()) {
        return null
      }

      // Return in Better Auth compatible format
      return {
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          emailVerified: user.emailVerified,
          phoneNumber: user.phoneNumber,
          phoneVerified: user.phoneVerified,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt,
        },
        session: {
          id: session.id,
          token: session.token,
          expiresAt: session.expiresAt,
          userId: session.userId,
        },
      }
    } catch (error) {
      this.logger.error('Session lookup error:', error)

      return null
    }
  }

  private extractSessionTokenFromRequest(request: Request): string | null {
    const cookies = request.headers.cookie

    if (cookies) {
      const cookieArray = cookies.split(';')
      for (const cookie of cookieArray) {
        const [name, value] = cookie.trim().split('=')

        // Check for both __Secure- prefixed and non-prefixed cookie names
        if (name === SESSION_COOKIE_NAME || name === SECURE_SESSION_COOKIE_NAME) {
          // Decode URL-encoded token and extract base token part
          const decodedToken = decodeURIComponent(value)

          return decodedToken.split('.')[0]
        }
      }
    }

    const authHeader = request.headers.authorization

    if (authHeader?.startsWith('Bearer ')) {
      const token = authHeader.substring(7)
      // For Bearer tokens, if it contains a dot, extract the base token part

      return token.includes('.') ? token.split('.')[0] : token
    }

    return null
  }

  async sendEmailOtp(email: string, type: 'sign-in' | 'email-verification' | 'password-reset' = 'sign-in') {
    const otp = Math.floor(100000 + Math.random() * 900000).toString()
    const expiresAt = new Date(Date.now() + OTP_EXPIRY_MS) // 5 minutes
    const now = new Date()

    await this.db.delete(verifications).where(eq(verifications.identifier, email))

    await this.db.insert(verifications).values({
      id: `email-${nanoid()}`,
      identifier: email,
      value: otp,
      expiresAt,
      createdAt: now,
      updatedAt: now,
    })

    await this.emailService.sendOTP(email, otp, type)

    return { success: true, message: 'Verification code sent to your email' }
  }

  async verifyEmailOtp(email: string, otp: string, response?: Response) {
    const verificationResult = await this.db
      .select()
      .from(verifications)
      .where(and(eq(verifications.identifier, email), eq(verifications.value, otp)))
      .limit(1)

    if (verificationResult.length === 0) {
      throw new BadRequestException('Invalid verification code')
    }

    const verification = verificationResult[0]

    if (verification.expiresAt < new Date()) {
      await this.db.delete(verifications).where(eq(verifications.id, verification.id))
      throw new BadRequestException('Verification code has expired')
    }

    await this.db.delete(verifications).where(eq(verifications.id, verification.id))

    const existingUserResult = await this.db.select().from(users).where(eq(users.email, email)).limit(1)

    let user

    if (existingUserResult.length === 0) {
      const now = new Date()
      const newUserId = `user-${nanoid()}`

      await this.db.insert(users).values({
        id: newUserId,
        email,
        emailVerified: true,
        name: email.split('@')[0],
        createdAt: now,
        updatedAt: now,
      })

      const [insertedUser] = await this.db.select().from(users).where(eq(users.id, newUserId)).limit(1)
      user = insertedUser
    } else {
      const [existingUser] = existingUserResult
      user = existingUser
      await this.db.update(users).set({ emailVerified: true, updatedAt: new Date() }).where(eq(users.id, user.id))
    }

    const session = await this.createBetterAuthSession(user.id, response)

    return {
      user,
      session,
      access_token: session.token,
    }
  }

  async sendPhoneOtp(userId: string, phoneNumber: string) {
    const userResult = await this.db.select().from(users).where(eq(users.id, userId)).limit(1)

    if (userResult.length === 0) {
      throw new UnauthorizedException('User not found')
    }

    const result = await this.smsService.sendVerification(phoneNumber)

    if (!result.success) {
      throw new BadRequestException('Failed to send verification code')
    }

    return { success: true, message: 'Verification code sent to your phone' }
  }

  async verifyPhoneOtp(userId: string, phoneNumber: string, otp: string) {
    const userResult = await this.db.select().from(users).where(eq(users.id, userId)).limit(1)

    if (userResult.length === 0) {
      throw new UnauthorizedException('User not found')
    }

    const result = await this.smsService.verifyCode(phoneNumber, otp)

    if (!result.success || !result.valid) {
      throw new BadRequestException('Invalid verification code')
    }

    await this.db
      .update(users)
      .set({
        phoneNumber,
        phoneVerified: true,
        updatedAt: new Date(),
      })
      .where(eq(users.id, userId))

    return { success: true, message: 'Phone number verified successfully' }
  }

  async signOut(request: Request, response: Response) {
    try {
      const sessionToken = this.extractSessionTokenFromRequest(request)

      if (sessionToken) {
        await this.db.delete(sessions).where(eq(sessions.token, sessionToken))
      }

      // Environment-based configuration
      const isProduction = process.env.NODE_ENV === 'production'

      // Clear all possible cookie variations to ensure complete logout
      const cookieOptions = {
        httpOnly: true,
        secure: isProduction, // Secure only in production (HTTPS)
        sameSite: isProduction ? ('lax' as const) : ('none' as const), // 'lax' for production subdomains, 'none' for dev cross-origin
        path: '/',
        // Add domain only in production for subdomain sharing
        ...(isProduction ? { domain: '.caishen.io' } : {}),
      }

      // Clear the main cookie
      response.clearCookie(SESSION_COOKIE_NAME, cookieOptions)

      // Clear the secure prefixed cookie (browsers may create this automatically)
      response.clearCookie(SECURE_SESSION_COOKIE_NAME, cookieOptions)

      // Also clear with different path variations just in case
      response.clearCookie(SESSION_COOKIE_NAME, { ...cookieOptions, path: '/auth' })
      response.clearCookie(SECURE_SESSION_COOKIE_NAME, { ...cookieOptions, path: '/auth' })

      this.logger.log('User signed out successfully, all cookies cleared')

      return { success: true, message: 'Signed out successfully' }
    } catch (error) {
      this.logger.error('Sign out error:', error)
      throw new Error(`Failed to sign out: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  async getSession(sessionToken?: string) {
    if (!sessionToken) return null

    const sessionResult = await this.db
      .select()
      .from(sessions)
      .leftJoin(users, eq(sessions.userId, users.id))
      .where(eq(sessions.token, sessionToken))
      .limit(1)

    if (sessionResult.length === 0 || !sessionResult[0].session) {
      return null
    }

    const { session } = sessionResult[0]

    if (session.expiresAt < new Date()) {
      return null
    }

    return session
  }

  async validateUser(userId: string) {
    const userResult = await this.db.select().from(users).where(eq(users.id, userId)).limit(1)

    return userResult.length > 0 ? userResult[0] : null
  }

  private async createBetterAuthSession(userId: string, response?: Response) {
    const sessionToken = nanoid(32)
    const expiresAt = new Date(Date.now() + SESSION_DURATION_MS)
    const now = new Date()
    const sessionId = `session-${nanoid()}`

    // Environment-based configuration
    const isProduction = process.env.NODE_ENV === 'production'

    await this.db.insert(sessions).values({
      id: sessionId,
      userId,
      token: sessionToken,
      expiresAt,
      createdAt: now,
      updatedAt: now,
    })

    if (response) {
      const cookieConfig = {
        httpOnly: true,
        secure: isProduction, // Secure only in production (HTTPS)
        sameSite: isProduction ? ('lax' as const) : ('none' as const), // 'lax' for production subdomains, 'none' for dev cross-origin
        maxAge: SESSION_DURATION_MS,
        path: '/',
        // Add domain only in production for subdomain sharing
        ...(isProduction ? { domain: '.caishen.io' } : {}),
        // Add partitioned for development cross-origin
        ...(isProduction ? {} : { partitioned: true }),
      }

      response.cookie(SESSION_COOKIE_NAME, sessionToken, cookieConfig)
    }

    return {
      id: sessionId,
      token: sessionToken,
      expiresAt,
      userId,
    }
  }
}
