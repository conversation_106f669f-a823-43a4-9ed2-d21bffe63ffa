import { Injectable } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { Twi<PERSON> } from 'twilio'

@Injectable()
export class SmsService {
  private twilio: Twilio

  constructor(private configService: ConfigService) {
    const accountSid = this.configService.get('twilio.accountSid')
    const authToken = this.configService.get('twilio.authToken')
    this.twilio = new Twilio(accountSid, authToken)
  }

  async sendVerification(phoneNumber: string): Promise<{ success: boolean; sid?: string }> {
    try {
      const verifyServiceSid = this.getVerifyServiceSid()

      if (!verifyServiceSid || verifyServiceSid === 'YOUR_VERIFY_SERVICE_SID') {
        throw new Error(
          'Twilio Verify Service SID not configured. Please set TWILIO_VERIFY_SERVICE_SID environment variable.',
        )
      }

      const verification = await this.twilio.verify.v2.services(verifyServiceSid).verifications.create({
        to: phoneNumber,
        channel: 'sms',
      })

      return {
        success: true,
        sid: verification.sid,
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      throw new Error(`Failed to send verification code: ${errorMessage}`)
    }
  }

  async verifyCode(phoneNumber: string, code: string): Promise<{ success: boolean; valid: boolean }> {
    try {
      const verifyServiceSid = this.getVerifyServiceSid()

      if (!verifyServiceSid || verifyServiceSid === 'YOUR_VERIFY_SERVICE_SID') {
        return { success: false, valid: false }
      }

      const verificationCheck = await this.twilio.verify.v2.services(verifyServiceSid).verificationChecks.create({
        to: phoneNumber,
        code,
      })

      return {
        success: true,
        valid: verificationCheck.status === 'approved',
      }
    } catch (error) {
      return {
        success: false,
        valid: false,
      }
    }
  }

  // Legacy method for backward compatibility (if needed)
  async sendOtp(phoneNumber: string, otp: string): Promise<void> {
    const fromNumber = this.configService.get('twilio.phoneNumber')

    await this.twilio.messages.create({
      body: `Your verification code is: ${otp}`,
      from: fromNumber,
      to: phoneNumber,
    })
  }

  validatePhoneNumber(phoneNumber: string): boolean {
    const phoneRegex = /^\+[1-9]\d{1,14}$/

    return phoneRegex.test(phoneNumber)
  }

  private getVerifyServiceSid(): string {
    // Read directly from process.env as a workaround for ConfigService issue
    const serviceSid =
      process.env.TWILIO_VERIFY_SERVICE_SID ||
      this.configService.get('twilio.verifyServiceSid') ||
      'YOUR_VERIFY_SERVICE_SID'
    // console.log('DEBUG - Twilio Verify Service SID:', serviceSid)
    // console.log('DEBUG - Raw env var TWILIO_VERIFY_SERVICE_SID:', process.env.TWILIO_VERIFY_SERVICE_SID)

    return serviceSid
  }
}
