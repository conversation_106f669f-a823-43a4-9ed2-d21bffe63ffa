import { Injectable, Inject } from '@nestjs/common'
import { sql, eq, and, or, desc, inArray, type InferSelectModel } from 'drizzle-orm'
import { NodePgDatabase } from 'drizzle-orm/node-postgres'

import { projects, projectCategories, projectHasCategories, projectAudits } from '../../../libs/database/schema'
import * as schema from '../../../libs/database/schema'

// Type definitions
type ProjectSelect = InferSelectModel<typeof projects>

interface GetApprovedProjectsParams {
  category?: 'upcoming'
  blockchain?: string
  search?: string
  page: number
  limit: number
}

@Injectable()
export class PublicProjectsService {
  constructor(@Inject('DB') private db: NodePgDatabase<typeof schema>) {}

  async getApprovedProjects(params: GetApprovedProjectsParams) {
    const { category, blockchain, search, page, limit } = params
    const offset = (page - 1) * limit

    // Build where conditions
    const whereConditions = [eq(projects.submissionStatus, 'liquidity_provided')]

    // Blockchain filter
    if (blockchain) {
      whereConditions.push(eq(projects.blockchainToLaunchOn, blockchain as ProjectSelect['blockchainToLaunchOn']))
    }

    // Search filter (project name or token symbol)
    if (search) {
      whereConditions.push(
        or(
          sql`LOWER(${projects.projectName}) LIKE ${`%${search.toLowerCase()}%`}`,
          sql`LOWER(${projects.tokenSymbol}) LIKE ${`%${search.toLowerCase()}%`}`,
        )!,
      )
    }

    // Category filter - for now only 'upcoming' which shows all liquidity_provided projects
    // No additional filtering needed since we already filter by liquidity_provided status
    // In the future, other categories can be added here if needed
    if (category) {
      // Currently only 'upcoming' is supported, which shows all liquidity_provided projects
    }

    // Get total count for pagination
    const [{ count: total }] = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(projects)
      .where(and(...whereConditions))

    // Get projects with categories
    const projectsList = await this.db
      .select({
        projectId: projects.projectId,
        projectName: projects.projectName,
        tokenSymbol: projects.tokenSymbol,
        blockchainToLaunchOn: projects.blockchainToLaunchOn,
        startDate: projects.expectedTgeLaunchDate,
        endDate: sql<string>`(${projects.expectedTgeLaunchDate} + INTERVAL '1 day' * ${projects.exclusiveTradingPeriodHours} / 24)::date`,
        status: projects.submissionStatus,
        initialPrice: projects.tgePriceUsdc,
        tokenThumbnailImageUrl: projects.tokenThumbnailImageUrl,
        projectWebsite: projects.projectWebsite,
        officialTwitter: projects.officialTwitter,
        discordUrl: projects.discordUrl,
        telegram: projects.telegram,
        farcaster: projects.farcaster,
        tokenId: projects.tokenId,
      })
      .from(projects)
      .where(and(...whereConditions))
      .orderBy(desc(projects.expectedTgeLaunchDate))
      .limit(limit)
      .offset(offset)

    // Get categories for each project
    const projectIds = projectsList.map((p) => p.projectId)
    const categoriesData = projectIds.length
      ? await this.db
          .select({
            projectId: projectHasCategories.projectId,
            categoryId: projectCategories.categoryId,
            categoryName: projectCategories.categoryName,
          })
          .from(projectHasCategories)
          .innerJoin(projectCategories, eq(projectHasCategories.categoryId, projectCategories.categoryId))
          .where(inArray(projectHasCategories.projectId, projectIds))
      : []

    // Group categories by project
    const categoriesByProject = categoriesData.reduce(
      (acc, cat) => {
        if (!acc[cat.projectId]) acc[cat.projectId] = []

        acc[cat.projectId].push({
          categoryId: cat.categoryId,
          categoryName: cat.categoryName,
        })

        return acc
      },
      {} as Record<string, { categoryId: number; categoryName: string }[]>,
    )

    // Format response
    const formattedProjects = projectsList.map((project) => ({
      projectId: project.projectId,
      projectName: project.projectName,
      tokenSymbol: project.tokenSymbol,
      blockchainToLaunchOn: project.blockchainToLaunchOn,
      startDate: project.startDate,
      endDate: project.endDate,
      status: project.status,
      initialPrice: project.initialPrice,
      tokenThumbnailImageUrl: project.tokenThumbnailImageUrl,
      tokenId: project.tokenId,
      categories: categoriesByProject[project.projectId] || [],
      socialLinks: {
        website: project.projectWebsite,
        twitter: project.officialTwitter,
        discord: project.discordUrl,
        telegram: project.telegram,
        farcaster: project.farcaster,
      },
    }))

    return {
      projects: formattedProjects,
      pagination: {
        page,
        limit,
        total,
        hasMore: offset + limit < total,
      },
    }
  }

  async getApprovedProjectById(projectId: string) {
    // Get project details - only if liquidity is provided (live projects)
    const projectResult = await this.db
      .select()
      .from(projects)
      .where(and(eq(projects.projectId, projectId), eq(projects.submissionStatus, 'liquidity_provided')))
      .limit(1)

    if (!projectResult.length) {
      return null
    }

    const project = projectResult[0]

    // Get project categories
    const categories = await this.db
      .select({
        categoryId: projectCategories.categoryId,
        categoryName: projectCategories.categoryName,
      })
      .from(projectHasCategories)
      .innerJoin(projectCategories, eq(projectHasCategories.categoryId, projectCategories.categoryId))
      .where(eq(projectHasCategories.projectId, projectId))

    // Get project audits
    const audits = await this.db
      .select({
        auditId: projectAudits.auditId,
        auditDate: projectAudits.auditDate,
        auditingFirm: projectAudits.auditingFirm,
        auditReportUrl: projectAudits.auditReportUrl,
        auditingFirmLogoUrl: projectAudits.auditingFirmLogoUrl,
      })
      .from(projectAudits)
      .where(eq(projectAudits.projectId, projectId))

    // Format and return complete project details
    return {
      projectId: project.projectId,
      projectName: project.projectName,
      projectWebsite: project.projectWebsite,
      blockchainToLaunchOn: project.blockchainToLaunchOn,

      // Rich text content
      projectDetails: project.projectDetails,
      teamIntroduction: project.teamIntroduction,
      teamPublicProfileLinks: project.teamPublicProfileLinks,
      fundraiseHistory: project.fundraiseHistory,
      totalRaisedUsd: project.totalRaisedUsd,
      tokenomicsDetails: project.tokenomicsDetails,
      preLaunchMarketingStrategy: project.preLaunchMarketingStrategy,
      headerDescription: project.headerDescription,

      // Social links
      socialLinks: {
        website: project.projectWebsite,
        twitter: project.officialTwitter,
        discord: project.discordUrl,
        telegram: project.telegram,
        farcaster: project.farcaster,
      },

      // Token information
      tokenInfo: {
        tokenName: project.tokenName,
        tokenSymbol: project.tokenSymbol,
        tokenContractAddress: project.tokenContractAddress,
        totalTokenSupply: project.totalTokenSupply,
        tokenLaunchMarketCapUsd: project.tokenLaunchMarketCapUsd,
        tokenThumbnailImageUrl: project.tokenThumbnailImageUrl,
      },

      // TGE details
      tgeDetails: {
        tgeType: project.tgeType,
        allocationTotalSupplyCaishen: project.allocationTotalSupplyCaishen,
        allocationCirculatingSupplyCaishen: project.allocationCirculatingSupplyCaishen,
        acceptedCurrenciesForPairing: project.acceptedCurrenciesForPairing,
        tgePriceUsdc: project.tgePriceUsdc,
        exclusiveTradingPeriodHours: project.exclusiveTradingPeriodHours,
        expectedTgeLaunchDate: project.expectedTgeLaunchDate,
        liquidityActivationDate: project.liquidityActivationDate,
        minAmountPerTradeSol: project.minAmountPerTradeSol,
        maxAmountPerTradeSol: project.maxAmountPerTradeSol,
      },

      // Related data
      categories,
      audits,

      // Metadata
      submissionStatus: project.submissionStatus,
      providedWalletAddress: project.providedWalletAddress,
      tokenId: project.tokenId,
      createdAt: project.createdAt,
      updatedAt: project.updatedAt,
    }
  }
}
