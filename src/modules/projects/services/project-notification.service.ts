import { readFileSync, existsSync } from 'fs'
import { join, resolve } from 'path'

import { Injectable, Logger } from '@nestjs/common'

import { getEnvConfig } from '../../../config/env'
import { EmailTransportService, type EmailSenderConfig } from '../../notifications/channels/email'

// Define proper interfaces for type safety
interface ProjectData {
  projectId: string
  projectName: string
  partnerName?: string
  contactName?: string
  contactEmail?: string
  expectedTgeLaunchDate?: string
  tokenSymbol?: string
}

interface UserData {
  email: string
  id: string
  name?: string
}

@Injectable()
export class ProjectNotificationService {
  private readonly logger = new Logger(ProjectNotificationService.name)
  private readonly senderConfig: EmailSenderConfig
  private readonly templatesPath: string

  constructor(private emailTransport: EmailTransportService) {
    const { mailCoach, templatePath } = getEnvConfig()
    this.senderConfig = {
      senderName: mailCoach.senderName,
      senderEmail: mailCoach.senderEmail,
    }

    // Use configured template path if provided, otherwise auto-detect
    if (templatePath) {
      this.templatesPath = resolve(templatePath)
      this.logger.log(`Using configured template path: ${this.templatesPath}`)
    } else {
      // Robust template path resolution for both dev and production
      const isDevelopment = process.env.NODE_ENV !== 'production'

      if (isDevelopment) {
        // In development: use source path
        this.templatesPath = resolve(process.cwd(), 'src', 'modules', 'projects', 'templates')
      } else {
        // In production: use dist path
        this.templatesPath = resolve(process.cwd(), 'dist', 'modules', 'projects', 'templates')
      }

      this.logger.log(`Auto-detected template path: ${this.templatesPath}`)
    }
  }

  async sendApplicationSubmittedToUser(userEmail: string, projectName: string): Promise<void> {
    try {
      const subject = 'Application Submitted'
      const html = this.generateUserSubmissionTemplate(projectName)

      await this.emailTransport.sendEmail(
        {
          to: userEmail,
          subject,
          html,
        },
        this.senderConfig,
      )

      this.logger.log(`Sent application submitted notification to user: ${userEmail}`)
    } catch (error) {
      this.logger.error(`Failed to send user notification to ${userEmail}:`, error)
      throw error
    }
  }

  async sendCuratedProjectSubmissionToAdmin(
    adminEmail: string,
    projectData: {
      projectName: string
      partnerName: string
      submitterName: string
      submitterEmail: string
      submissionTime: string
    },
  ): Promise<void> {
    try {
      const subject = 'Curated Project Submission'
      const html = this.generateAdminNotificationTemplate(projectData)

      await this.emailTransport.sendEmail(
        {
          to: adminEmail,
          subject,
          html,
        },
        this.senderConfig,
      )

      this.logger.log(`Sent curated project submission notification to admin: ${adminEmail}`)
    } catch (error) {
      this.logger.error(`Failed to send admin notification to ${adminEmail}:`, error)
      throw error
    }
  }

  async sendProjectSubmissionEmails(project: ProjectData, user: UserData): Promise<void> {
    // Validate email before sending
    if (!user.email || !this.isValidEmail(user.email)) {
      throw new Error(`Invalid user email address: ${user.email}`)
    }

    try {
      // Send confirmation to user
      await this.sendApplicationSubmittedToUser(user.email, project.projectName)

      // Send notification to admin
      const { adminNotificationEmail } = getEnvConfig()

      if (!adminNotificationEmail) {
        throw new Error('adminNotificationEmail is not set in environment config')
      }

      await this.sendCuratedProjectSubmissionToAdmin(adminNotificationEmail, {
        projectName: project.projectName,
        partnerName: project.partnerName || 'N/A',
        submitterName: project.contactName || user.email,
        submitterEmail: user.email,
        submissionTime: new Date().toISOString(),
      })

      this.logger.log(`Sent all submission notifications for project ${project.projectId}`)
    } catch (error) {
      this.logger.error(`Failed to send submission notifications for project ${project.projectId}:`, error)
      throw error
    }
  }

  async sendWalletSubmissionEmail(project: ProjectData, user: UserData): Promise<void> {
    // Validate email before sending
    if (!user.email || !this.isValidEmail(user.email)) {
      throw new Error(`Invalid user email address: ${user.email}`)
    }

    try {
      const subject = 'Wallet Address Submitted'
      const html = this.generateWalletSubmissionTemplate(project)

      await this.emailTransport.sendEmail(
        {
          to: user.email,
          subject,
          html,
        },
        this.senderConfig,
      )

      this.logger.log(`Sent wallet submission confirmation to user: ${user.email} for project: ${project.projectId}`)
    } catch (error) {
      this.logger.error(`Failed to send wallet submission email to ${user.email}:`, error)
      throw error
    }
  }

  async sendFeePaymentConfirmationEmail(project: ProjectData, user: UserData): Promise<void> {
    // Validate email before sending
    if (!user.email || !this.isValidEmail(user.email)) {
      throw new Error(`Invalid user email address: ${user.email}`)
    }

    try {
      const subject = 'Launch Fee Payment Confirmed'
      const html = this.generateFeePaymentConfirmationTemplate(project)

      await this.emailTransport.sendEmail(
        {
          to: user.email,
          subject,
          html,
        },
        this.senderConfig,
      )

      this.logger.log(`Sent fee payment confirmation to user: ${user.email} for project: ${project.projectId}`)
    } catch (error) {
      this.logger.error(`Failed to send fee payment confirmation email to ${user.email}:`, error)
      throw error
    }
  }

  private generateUserSubmissionTemplate(projectName: string): string {
    const templatePath = join(this.templatesPath, 'user-submission.html')

    if (!existsSync(templatePath)) {
      throw new Error(`User submission template not found at: ${templatePath}`)
    }

    try {
      const template = readFileSync(templatePath, 'utf-8')

      return this.replaceTemplateVariables(template, {
        projectName,
        currentYear: new Date().getFullYear().toString(),
      })
    } catch (error) {
      throw new Error(
        `Failed to read user submission template: ${error instanceof Error ? error.message : 'Unknown error'}`,
      )
    }
  }

  private generateAdminNotificationTemplate(projectData: {
    projectName: string
    partnerName: string
    submitterName: string
    submitterEmail: string
    submissionTime: string
  }): string {
    const templatePath = join(this.templatesPath, 'admin-notification.html')

    if (!existsSync(templatePath)) {
      throw new Error(`Admin notification template not found at: ${templatePath}`)
    }

    try {
      const template = readFileSync(templatePath, 'utf-8')

      return this.replaceTemplateVariables(template, {
        projectName: projectData.projectName,
        partnerName: projectData.partnerName,
        submitterName: projectData.submitterName,
        submitterEmail: projectData.submitterEmail,
        submissionTime: projectData.submissionTime,
        adminPanelUrl: this.getAdminPanelUrl(),
        currentYear: new Date().getFullYear().toString(),
      })
    } catch (error) {
      throw new Error(
        `Failed to read admin notification template: ${error instanceof Error ? error.message : 'Unknown error'}`,
      )
    }
  }

  private generateWalletSubmissionTemplate(project: ProjectData): string {
    const templatePath = join(this.templatesPath, 'wallet-submitted.html')

    if (!existsSync(templatePath)) {
      throw new Error(`Wallet submission template not found at: ${templatePath}`)
    }

    try {
      const template = readFileSync(templatePath, 'utf-8')

      // Calculate deadlines based on TGE date
      const tgeDate = new Date(project.expectedTgeLaunchDate || new Date())
      const launchFeeDeadline = new Date(tgeDate)
      launchFeeDeadline.setDate(tgeDate.getDate() - 3) // 72 hours before TGE

      const liquidityProvisioningDeadline = new Date(tgeDate)
      liquidityProvisioningDeadline.setDate(tgeDate.getDate() - 1) // 24 hours before TGE

      // Format dates in a readable format
      const formatDate = (date: Date) =>
        date.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit',
          timeZone: 'UTC',
          timeZoneName: 'short',
        })

      return this.replaceTemplateVariables(template, {
        projectName: project.projectName,
        tokenSymbol: project.tokenSymbol || 'TOKEN',
        scheduledTgeDate: formatDate(tgeDate),
        launchFeeDeadline: formatDate(launchFeeDeadline),
        liquidityProvisioningDeadline: formatDate(liquidityProvisioningDeadline),
        payLaunchFeeUrl: this.getPayLaunchFeeUrl(project.projectId),
        currentYear: new Date().getFullYear().toString(),
      })
    } catch (error) {
      throw new Error(
        `Failed to read wallet submission template: ${error instanceof Error ? error.message : 'Unknown error'}`,
      )
    }
  }

  private generateFeePaymentConfirmationTemplate(project: ProjectData): string {
    const templatePath = join(this.templatesPath, 'fee-payment-confirmed.html')

    if (!existsSync(templatePath)) {
      throw new Error(`Fee payment confirmation template not found at: ${templatePath}`)
    }

    try {
      const template = readFileSync(templatePath, 'utf-8')

      // Calculate deadlines based on TGE date (same as wallet submission)
      const tgeDate = new Date(project.expectedTgeLaunchDate || new Date())
      const liquidityProvisioningDeadline = new Date(tgeDate)
      liquidityProvisioningDeadline.setDate(tgeDate.getDate() - 1) // 24 hours before TGE

      // Format dates in a readable format
      const formatDate = (date: Date) =>
        date.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit',
          timeZone: 'UTC',
          timeZoneName: 'short',
        })

      return this.replaceTemplateVariables(template, {
        projectName: project.projectName,
        tokenSymbol: project.tokenSymbol || 'TOKEN',
        scheduledTgeDate: formatDate(tgeDate),
        liquidityProvisioningDeadline: formatDate(liquidityProvisioningDeadline),
        currentYear: new Date().getFullYear().toString(),
      })
    } catch (error) {
      throw new Error(
        `Failed to read fee payment confirmation template: ${error instanceof Error ? error.message : 'Unknown error'}`,
      )
    }
  }

  private replaceTemplateVariables(template: string, variables: Record<string, string>): string {
    let result = template

    for (const [key, value] of Object.entries(variables)) {
      const placeholder = `{{${key}}}`
      result = result.replace(new RegExp(placeholder, 'g'), value)
    }

    return result
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/

    return emailRegex.test(email)
  }

  private getAdminPanelUrl(): string {
    const { adminPanelUrl } = getEnvConfig()

    // Use configured admin panel URL if available, otherwise use default
    return adminPanelUrl || ''
  }

  private getPayLaunchFeeUrl(projectId: string): string {
    return `https://launch.caishen.io/launch/${projectId}`
  }
}
