import { ApiProperty } from '@nestjs/swagger'
import { createZodDto } from 'nestjs-zod'
import { z } from 'zod'

const updateProjectSchema = z.object({
  projectName: z.string().min(1).max(255).optional(),
  projectWebsite: z.string().url().max(2048).optional(),
  contactName: z.string().min(1).max(255).optional(),
  contactEmail: z.string().email().max(255).optional(),
  roleInProject: z.string().min(1).max(255).optional(),
  blockchainToLaunchOn: z.enum(['ethereum', 'solana', 'polygon', 'bsc', 'arbitrum', 'base', 'optimism']).optional(),
  currentFormStep: z.number().min(1).max(11).optional(),

  // Page 2 fields
  projectDetails: z.any().optional(),
  teamIntroduction: z.any().optional(),

  // Page 3 fields
  teamPublicProfileLinks: z.string().optional(),

  // Page 4 fields
  fundraiseHistory: z.any().optional(),
  totalRaisedUsd: z.string().optional(),

  // Page 5 fields
  officialTwitter: z.string().url().max(2048).optional(),
  discordUrl: z.string().url().max(2048).optional(),
  telegram: z.string().max(2048).optional(),
  farcaster: z.string().max(2048).optional(),
  communitySizeEngagement: z.any().optional(),

  // Page 6 fields
  tokenomicsDetails: z.any().optional(),

  // Page 7 fields
  securityAuditsGeneralInfo: z.any().optional(),

  // Page 8 fields
  tokenName: z.string().max(255).optional(),
  tokenSymbol: z.string().max(50).optional(),
  tokenContractAddress: z.string().max(255).optional(),
  totalTokenSupply: z.string().optional(),
  tokenLaunchMarketCapUsd: z.string().optional(),
  tokenThumbnailImageUrl: z.string().url().max(2048).optional(),

  // Page 9 fields
  tgeType: z.enum(['fixed_price', 'auction', 'bonding_curve']).optional(),
  allocationTotalSupplyCaishen: z.string().optional(),
  allocationCirculatingSupplyCaishen: z.string().optional(),
  acceptedCurrenciesForPairing: z.string().max(255).optional(),
  tgePriceSol: z.string().optional(),
  exclusiveTradingPeriodHours: z.number().optional(),
  expectedTgeLaunchDate: z.string().optional(),
  liquidityActivationDate: z.string().optional(),
  minAmountPerTradeSol: z.string().optional(),
  maxAmountPerTradeSol: z.string().optional(),

  // Page 10 fields
  preLaunchMarketingStrategy: z.any().optional(),

  // Page 11 fields
  headerDescription: z.any().optional(),
  bannerBackgroundImageUrl: z.string().url().max(2048).optional(),

  // Post-approval fields
  providedWalletAddress: z
    .string()
    .max(255)
    .refine(
      (address) => {
        if (!address) return true // Optional field

        // EVM addresses (Ethereum, Polygon, BSC, Arbitrum, Base, Optimism)
        const evmRegex = /^(0x)[0-9A-Fa-f]{40}$/
        // Solana addresses
        const solanaRegex = /^[1-9A-HJ-NP-Za-km-z]{32,44}$/

        return evmRegex.test(address) || solanaRegex.test(address)
      },
      {
        message: 'Invalid wallet address format. Must be a valid EVM address (0x...) or Solana address',
      },
    )
    .optional(),
})

export class UpdateProjectDto extends createZodDto(updateProjectSchema) {
  @ApiProperty({
    example: 'DeFi Protocol Updated',
    description: 'Updated project name',
    required: false,
  })
  projectName?: string

  @ApiProperty({
    example: 'https://newexample.com',
    description: 'Updated website URL',
    required: false,
  })
  projectWebsite?: string

  @ApiProperty({
    example: 2,
    description: 'Current form step (1-11)',
    required: false,
  })
  currentFormStep?: number

  @ApiProperty({
    example: 'DeFi Revolution Token',
    description: 'Token name',
    required: false,
  })
  tokenName?: string

  @ApiProperty({
    example: 'DEFIR',
    description: 'Token symbol',
    required: false,
  })
  tokenSymbol?: string

  @ApiProperty({
    example: '******************************************',
    description: 'Token contract address',
    required: false,
  })
  tokenContractAddress?: string

  @ApiProperty({
    example: '1000000000',
    description: 'Total token supply',
    required: false,
  })
  totalTokenSupply?: string

  @ApiProperty({
    example: '5000000.00',
    description: 'Token launch market cap in USD',
    required: false,
  })
  tokenLaunchMarketCapUsd?: string

  @ApiProperty({
    description: 'Tokenomics details (Editor.js format)',
    required: false,
  })
  tokenomicsDetails?: any

  @ApiProperty({
    description: 'Security audits general information (Editor.js format)',
    required: false,
  })
  securityAuditsGeneralInfo?: any

  @ApiProperty({
    description: 'Pre-launch marketing strategy (Editor.js format)',
    required: false,
  })
  preLaunchMarketingStrategy?: any

  @ApiProperty({
    description: 'Header description (Editor.js format)',
    required: false,
  })
  headerDescription?: any

  @ApiProperty({
    example: 'https://linkedin.com/in/founder, https://github.com/cofounder',
    description: 'Comma-separated team member public profile links',
    required: false,
  })
  teamPublicProfileLinks?: string

  @ApiProperty({
    example: '******************************************',
    description:
      'Wallet address provided after project approval for token launch. Must be a valid EVM address (0x...) or Solana address',
    required: false,
    pattern: '^(0x)[0-9A-Fa-f]{40}$|^[1-9A-HJ-NP-Za-km-z]{32,44}$',
  })
  providedWalletAddress?: string
}
