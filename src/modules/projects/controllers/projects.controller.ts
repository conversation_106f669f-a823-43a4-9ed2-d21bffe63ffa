import {
  Controller,
  Logger,
  Post,
  Get,
  Put,
  Delete,
  Body,
  Param,
  Request,
  UseGuards,
  HttpCode,
  HttpStatus,
} from '@nestjs/common'
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger'

import { CookieAuthGuard } from '../../auth/guards/cookie-auth.guard'
import { CreateProjectDto } from '../dto/create-project.dto'
import { SubmitWalletDto } from '../dto/submit-wallet.dto'
import { UpdateProjectDto } from '../dto/update-project.dto'
import { ProjectsService } from '../projects.service'

@ApiTags('Projects')
@Controller('projects')
@UseGuards(CookieAuthGuard)
@ApiBearerAuth()
export class ProjectsController {
  private readonly logger = new Logger(ProjectsController.name)

  constructor(private readonly projectsService: ProjectsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new project' })
  @ApiResponse({ status: 201, description: 'Project created successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async createProject(@Request() req: any, @Body() createProjectDto: CreateProjectDto) {
    const userId = req.user.id

    return this.projectsService.create(userId, createProjectDto)
  }

  @Get()
  @ApiOperation({ summary: 'Get all projects for the authenticated user' })
  @ApiResponse({ status: 200, description: 'Projects retrieved successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getUserProjects(@Request() req: any) {
    const userId = req.user.id

    return this.projectsService.findByUserId(userId)
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a specific project by ID' })
  @ApiResponse({ status: 200, description: 'Project retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Project not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getProject(@Request() req: any, @Param('id') projectId: string) {
    const userId = req.user.id

    return this.projectsService.findByIdAndUserId(projectId, userId)
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update a project' })
  @ApiResponse({ status: 200, description: 'Project updated successfully' })
  @ApiResponse({ status: 404, description: 'Project not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async updateProject(@Request() req: any, @Param('id') projectId: string, @Body() updateProjectDto: UpdateProjectDto) {
    const userId = req.user.id

    return this.projectsService.update(projectId, userId, updateProjectDto)
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete a project (only drafts)' })
  @ApiResponse({ status: 204, description: 'Project deleted successfully' })
  @ApiResponse({ status: 404, description: 'Project not found' })
  @ApiResponse({ status: 400, description: 'Cannot delete non-draft projects' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async deleteProject(@Request() req: any, @Param('id') projectId: string) {
    const userId = req.user.id
    await this.projectsService.delete(projectId, userId)
  }

  @Post(':id/submit')
  @ApiOperation({
    summary: 'Submit a project for review',
    description:
      'Changes project status from draft to submitted and sends email notifications. This endpoint requires only the project ID as a path parameter - no request body is needed.',
  })
  @ApiResponse({
    status: 200,
    description: 'Project submitted successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: {
          type: 'string',
          example: 'Project submitted successfully. You will receive a confirmation email shortly.',
        },
        project: {
          type: 'object',
          properties: {
            projectId: { type: 'string', example: 'a356fe0c-4cb7-4fe2-a45b-06c2faf02c36' },
            projectName: { type: 'string', example: 'DeFi Revolution' },
            submissionStatus: { type: 'string', example: 'submitted' },
            submittedAt: { type: 'string', example: '2025-06-13T05:02:30.842Z' },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Project cannot be submitted (missing fields or wrong status)',
  })
  @ApiResponse({ status: 404, description: 'Project not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async submitProject(@Request() req: any, @Param('id') projectId: string) {
    const userId = req.user.id

    return this.projectsService.submitProject(projectId, userId)
  }

  @Post(':id/submit-wallet')
  @ApiOperation({
    summary: 'Submit wallet address for approved project',
    description:
      'Submit a wallet address for an approved project. Changes project status from approved to wallet_provided and sends confirmation email.',
  })
  @ApiResponse({
    status: 200,
    description: 'Wallet address submitted successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: {
          type: 'string',
          example: 'Wallet address submitted successfully. You will receive a confirmation email shortly.',
        },
        project: {
          type: 'object',
          properties: {
            projectId: { type: 'string', example: 'a356fe0c-4cb7-4fe2-a45b-06c2faf02c36' },
            projectName: { type: 'string', example: 'DeFi Revolution' },
            tokenSymbol: { type: 'string', example: 'DEFI' },
            submissionStatus: { type: 'string', example: 'wallet_provided' },
            providedWalletAddress: { type: 'string', example: '******************************************' },
            updatedAt: { type: 'string', example: '2025-06-13T05:02:30.842Z' },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid wallet address or project status',
  })
  @ApiResponse({ status: 404, description: 'Project not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async submitWallet(@Request() req: any, @Param('id') projectId: string, @Body() submitWalletDto: SubmitWalletDto) {
    const userId = req.user.id

    return this.projectsService.submitWalletAddress(projectId, submitWalletDto.walletAddress, userId)
  }
}
