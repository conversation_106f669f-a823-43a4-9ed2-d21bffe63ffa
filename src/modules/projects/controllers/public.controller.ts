import { Controller, Get, Param, Query, NotFoundException } from '@nestjs/common'
import { ApiTags, ApiOperation, ApiResponse, ApiQuery, ApiParam } from '@nestjs/swagger'

import { ProjectsService } from '../projects.service'
import { PublicProjectsService } from '../services/public-projects.service'

@ApiTags('Public Projects')
@Controller('public/projects')
export class PublicController {
  constructor(
    private readonly publicProjectsService: PublicProjectsService,
    private readonly projectsService: ProjectsService,
  ) {}

  @Get()
  @ApiOperation({
    summary: 'Get list of live projects with liquidity provided',
    description: `
    Returns a list of live projects with liquidity provided for public consumption.
    
    **Filters:**
    - Category filter: Currently only 'upcoming' which shows all live projects
    - Blockchain filter: Filter by blockchain network
    - Search: Search by project name or token symbol
    
    **Note:** Only projects with status = 'liquidity_provided' are returned (projects that are live and trading).
    `,
  })
  @ApiQuery({
    name: 'category',
    required: false,
    enum: ['upcoming'],
    description: 'Filter by project category (currently only upcoming available)',
  })
  @ApiQuery({ name: 'blockchain', required: false, description: 'Filter by blockchain network' })
  @ApiQuery({ name: 'search', required: false, description: 'Search by project name or token symbol' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page (default: 20, max: 100)' })
  @ApiResponse({
    status: 200,
    description: 'Live projects with liquidity provided retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        projects: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              projectId: { type: 'string', example: 'a356fe0c-4cb7-4fe2-a45b-06c2faf02c36' },
              projectName: { type: 'string', example: 'DeFi Revolution' },
              tokenSymbol: { type: 'string', example: 'DEFI' },
              blockchainToLaunchOn: { type: 'string', example: 'ethereum' },
              startDate: { type: 'string', example: '2025-07-01' },
              endDate: { type: 'string', example: '2025-07-08' },
              status: { type: 'string', example: 'liquidity_provided' },
              initialPrice: { type: 'string', example: '0.001000000' },
              tokenThumbnailImageUrl: { type: 'string', example: 'https://example.com/logo.png' },
              categories: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    categoryId: { type: 'number', example: 1 },
                    categoryName: { type: 'string', example: 'DeFi' },
                  },
                },
              },
              socialLinks: {
                type: 'object',
                properties: {
                  twitter: { type: 'string', example: 'https://twitter.com/project' },
                  discord: { type: 'string', example: 'https://discord.gg/project' },
                  telegram: { type: 'string', example: 'https://t.me/project' },
                  website: { type: 'string', example: 'https://project.com' },
                },
              },
            },
          },
        },
        pagination: {
          type: 'object',
          properties: {
            page: { type: 'number', example: 1 },
            limit: { type: 'number', example: 20 },
            total: { type: 'number', example: 45 },
            hasMore: { type: 'boolean', example: true },
          },
        },
      },
    },
  })
  async getApprovedProjects(
    @Query('category') category?: 'upcoming',
    @Query('blockchain') blockchain?: string,
    @Query('search') search?: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
  ) {
    return this.publicProjectsService.getApprovedProjects({
      category,
      blockchain,
      search,
      page: page || 1,
      limit: Math.min(limit || 20, 100),
    })
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Get full project details by ID',
    description: `
    Returns complete details about a live project by its ID.
    
    **Restrictions:**
    - Only projects with liquidity provided can be accessed
    - Returns 404 if project doesn't have liquidity provided or doesn't exist
    
    **Returns all project information including:**
    - Basic details (name, description, website)
    - Token information (symbol, supply, price)
    - TGE details (dates, trading period)
    - Team and social links
    - Categories and audits
    - Rich text content (project details, tokenomics, etc.)
    `,
  })
  @ApiParam({
    name: 'id',
    description: 'Project ID',
    example: 'a356fe0c-4cb7-4fe2-a45b-06c2faf02c36',
  })
  @ApiResponse({
    status: 200,
    description: 'Project details retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        projectId: { type: 'string' },
        projectName: { type: 'string' },
        projectWebsite: { type: 'string' },
        blockchainToLaunchOn: { type: 'string' },
        projectDetails: { type: 'object', description: 'Rich text content (Editor.js format)' },
        teamIntroduction: { type: 'object', description: 'Rich text content (Editor.js format)' },
        teamPublicProfileLinks: { type: 'string' },
        fundraiseHistory: { type: 'object', description: 'Rich text content (Editor.js format)' },
        totalRaisedUsd: { type: 'string' },
        socialLinks: {
          type: 'object',
          properties: {
            twitter: { type: 'string' },
            discord: { type: 'string' },
            telegram: { type: 'string' },
            farcaster: { type: 'string' },
            website: { type: 'string' },
          },
        },
        tokenomicsDetails: { type: 'object', description: 'Rich text content (Editor.js format)' },
        tokenInfo: {
          type: 'object',
          properties: {
            tokenName: { type: 'string' },
            tokenSymbol: { type: 'string' },
            tokenContractAddress: { type: 'string' },
            totalTokenSupply: { type: 'string' },
            tokenLaunchMarketCapUsd: { type: 'string' },
            tokenThumbnailImageUrl: { type: 'string' },
          },
        },
        tgeDetails: {
          type: 'object',
          properties: {
            tgeType: { type: 'string' },
            allocationTotalSupplyCaishen: { type: 'string' },
            allocationCirculatingSupplyCaishen: { type: 'string' },
            acceptedCurrenciesForPairing: { type: 'string' },
            tgePriceSol: { type: 'string' },
            exclusiveTradingPeriodHours: { type: 'number' },
            expectedTgeLaunchDate: { type: 'string' },
            liquidityActivationDate: { type: 'string' },
            minAmountPerTradeSol: { type: 'string' },
            maxAmountPerTradeSol: { type: 'string' },
          },
        },
        categories: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              categoryId: { type: 'number' },
              categoryName: { type: 'string' },
            },
          },
        },
        audits: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              auditId: { type: 'string' },
              auditDate: { type: 'string' },
              auditingFirm: { type: 'string' },
              auditReportUrl: { type: 'string' },
              auditingFirmLogoUrl: { type: 'string' },
            },
          },
        },
        submissionStatus: { type: 'string', example: 'liquidity_provided' },
        providedWalletAddress: {
          type: 'string',
          example: '******************************************',
          pattern: '^(0x)[0-9A-Fa-f]{40}$|^[1-9A-HJ-NP-Za-km-z]{32,44}$',
          description: 'Wallet address (EVM or Solana format)',
        },
        createdAt: { type: 'string' },
        updatedAt: { type: 'string' },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Project not found or liquidity not provided',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 404 },
        message: { type: 'string', example: 'Project not found or liquidity not provided' },
        error: { type: 'string', example: 'Not Found' },
      },
    },
  })
  async getProjectById(@Param('id') projectId: string) {
    const project = await this.publicProjectsService.getApprovedProjectById(projectId)

    if (!project) {
      throw new NotFoundException('Project not found or liquidity not provided')
    }

    return project
  }
}
