import { Modu<PERSON> } from '@nestjs/common'
import { ConfigModule } from '@nestjs/config'

import { AdminController } from './controllers/admin.controller'
import { AuditsController } from './controllers/audits.controller'
import { CategoriesController } from './controllers/categories.controller'
import { ProjectsController } from './controllers/projects.controller'
import { PublicController } from './controllers/public.controller'
import { ProjectsService } from './projects.service'
import { AdminService } from './services/admin.service'
import { AuditsService } from './services/audits.service'
import { CategoriesService } from './services/categories.service'
import { LaunchpadTokenService } from './services/launchpad-token.service'
import { ProjectNotificationService } from './services/project-notification.service'
import { ProjectStatusService } from './services/project-status.service'
import { ProjectValidationService } from './services/project-validation.service'
import { PublicProjectsService } from './services/public-projects.service'
import { DatabaseModule } from '../../libs/database/database.module'
import { AuthModule } from '../auth/auth.module'
import { ChainsModule } from '../chains/chains.module'
import { NotificationsModule } from '../notifications/notifications.module'
import { TokensModule } from '../tokens/tokens.module'

@Module({
  imports: [ConfigModule, DatabaseModule, AuthModule, ChainsModule, NotificationsModule, TokensModule],
  controllers: [ProjectsController, CategoriesController, AuditsController, AdminController, PublicController],
  providers: [
    ProjectsService,
    CategoriesService,
    AuditsService,
    AdminService,
    ProjectValidationService,
    ProjectNotificationService,
    ProjectStatusService,
    PublicProjectsService,
    LaunchpadTokenService,
  ],
  exports: [
    ProjectsService,
    CategoriesService,
    AuditsService,
    AdminService,
    ProjectValidationService,
    ProjectNotificationService,
    ProjectStatusService,
    PublicProjectsService,
    LaunchpadTokenService,
  ],
})
export class ProjectsModule {}
