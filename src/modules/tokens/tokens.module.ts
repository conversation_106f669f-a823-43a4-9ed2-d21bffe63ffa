import { Module } from '@nestjs/common'
import { ScheduleModule } from '@nestjs/schedule'

import { IndexerPriceClient } from './clients/indexer-price.client'
import { TokensStorage } from './storage'
import { TokensController } from './tokens.controller'
import { TokensService } from './tokens.service'
import { DatabaseModule } from '../../libs/database/database.module'
import { ChainsModule } from '../chains'
import { FirebaseModule } from '../firebase/firebase.module'

@Module({
  imports: [FirebaseModule, ChainsModule, ScheduleModule.forRoot(), DatabaseModule],
  controllers: [TokensController],
  providers: [TokensService, TokensStorage, IndexerPriceClient],
  exports: [TokensService, TokensStorage, IndexerPriceClient],
})
export class TokensModule {}
