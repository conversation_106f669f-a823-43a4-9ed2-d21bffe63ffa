import { Module } from '@nestjs/common'

import { ChainsModule } from '@/modules/chains'
import { TokensModule } from '@/modules/tokens'

import { IndexerClient } from './clients/indexer'
import { HistoryController } from './history.controller'
import { HistoryService } from './history.service'
import { FirebaseModule } from '../firebase'

@Module({
  exports: [HistoryService],
  imports: [FirebaseModule, TokensModule, ChainsModule],
  providers: [HistoryService, IndexerClient],
  controllers: [HistoryController],
})
export class HistoryModule {}
