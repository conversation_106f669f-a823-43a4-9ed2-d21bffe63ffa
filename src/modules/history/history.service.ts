import { Injectable } from '@nestjs/common'

import { IndexerClient } from './clients/indexer'
import type { TxInfo } from './schemas/tx-info.schema'

@Injectable()
export class HistoryService {
  constructor(private readonly indexerClient: IndexerClient) {}

  async getTxInfoByIntentId(id: number): Promise<TxInfo | undefined> {
    return this.indexerClient.getIntentInfo(id)
  }

  async getHistoryBySignerAddress(address: string): Promise<TxInfo[]> {
    const intents = await this.indexerClient.getIntentHistory(address)
    const deposits = await this.indexerClient.getDepositHistory(address)
    const totalHistory = intents.concat(deposits)

    totalHistory.sort((a, b) => b.timestamp - a.timestamp)

    return totalHistory
  }
}
