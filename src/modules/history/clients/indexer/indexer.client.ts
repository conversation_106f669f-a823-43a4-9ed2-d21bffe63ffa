import { Injectable, Logger } from '@nestjs/common'
import axios from 'axios'
import dayjs from 'dayjs'
import { z } from 'zod'

import { getEnvConfig } from '@/config/env'
import { Big } from '@/libs/big-number'
import { ChainsService } from '@/modules/chains'
import { TokensService } from '@/modules/tokens'
import { fromMinorUnit } from '@/utils/amount/minor-unit'
import { getErrorMessage } from '@/utils/get-error-message'
import { getExplorerLinkForIntent } from '@/utils/get-explorer-link'

import { type IndexerDeposit, indexerDepositSchema } from './indexer-deposit.schema'
import { type IndexerIntent, indexerIntentSchema } from './indexer-intent.schema'
import { mapAddress } from './mappers/address.mapper'
import { mapDepositLink } from './mappers/link.explorer'
import { mapStatus } from './mappers/status.mapper'
import { mapSwapType } from './mappers/swap.mapper'
import { mapTxType } from './mappers/type.mapper'
import {
  type DepositTxInfo,
  type SendTxInfo,
  type SwapTxInfo,
  type TxInfo,
  txInfoSchema,
  depositTxInfoSchema,
} from '../../schemas/tx-info.schema'

@Injectable()
export class IndexerClient {
  private logger = new Logger('IndexerClient')
  private client = axios.create({
    baseURL: getEnvConfig().indexer.apiUrl,
  })

  constructor(
    private readonly tokensService: TokensService,
    private readonly chainsService: ChainsService,
  ) {}

  async getIntentInfo(intentId: number): Promise<TxInfo | undefined> {
    try {
      const { data: intentInfo } = await this.client.get<IndexerIntent>(`/intents/${intentId}`)
      indexerIntentSchema.parse(intentInfo)

      return await this.mapIntentToTxInfo(intentInfo)
    } catch (error) {
      const errorMessage = `Could not parse intent info from indexer for ${intentId}, error: ${getErrorMessage(error)}`
      this.logger.error(errorMessage)

      return undefined
    }
  }

  async getIntentHistory(address: string): Promise<TxInfo[]> {
    try {
      const { data: intentInfo } = await this.client.get<IndexerIntent[]>(`/intents/history/${address}`, {
        params: { per_page: 100 },
      })
      z.array(indexerIntentSchema).parse(intentInfo)
      const promises = intentInfo.map(this.mapIntentToTxInfo.bind(this))
      const mapped = await Promise.allSettled(promises)
      const success = mapped.filter((m) => m.status === 'fulfilled')
      const failed = mapped.filter((m) => m.status === 'rejected')

      if (failed.length) {
        failed.forEach((f) => this.logger.error(`Failed to map intent to tx info: ${f.reason}`))
      }

      return success.map((m) => m.value)
    } catch (error) {
      const errorMessage = `Could not parse history from indexer, error: ${getErrorMessage(error)}`
      this.logger.error(errorMessage)

      throw Error(errorMessage)
    }
  }

  async getDepositHistory(userAddress: string): Promise<DepositTxInfo[]> {
    try {
      const { data: depositInfo } = await this.client.get<IndexerDeposit[]>(`/get_deposit_history/${userAddress}`)
      z.array(indexerDepositSchema).parse(depositInfo)

      const promises = depositInfo.map(this.mapDepositToTxInfo.bind(this))
      const mapped = await Promise.allSettled(promises)
      const success = mapped.filter((m) => m.status === 'fulfilled')
      const failed = mapped.filter((m) => m.status === 'rejected')

      if (failed.length) {
        failed.forEach((f) => this.logger.error(`Failed to map intent to tx info: ${f.reason}`))
      }

      return success.map((m) => m.value)
    } catch (error) {
      const errorMessage = `Could not parse deposit history from indexer, error: ${getErrorMessage(error)}`
      this.logger.error(errorMessage)

      throw Error(errorMessage)
    }
  }

  private async mapIntentToTxInfo(tx: IndexerIntent): Promise<TxInfo> {
    try {
      const status = mapStatus(tx)
      const type = mapTxType(tx)
      const timestamp = dayjs(tx.createdAt).unix() * 1000
      const weiAmountFrom = tx.source?.amountIn || tx.initial_data?.amount_in
      const weiAmountTo = tx.destination?.amountOut || tx.initial_data.amount_out

      const hexContractFrom = tx.source?.tokenIn || tx.initial_data?.token_in || undefined
      const chainIdFrom = Number(tx.source?.chainId || tx.initial_data?.origin_chain || undefined)
      const hexContractTo = tx.source?.tokenOut || tx.initial_data?.token_out || undefined
      const chainIdTo = Number(tx.destination?.chainId || tx.initial_data?.target_chain || undefined)

      if (
        !status ||
        !weiAmountFrom ||
        !weiAmountTo ||
        !hexContractFrom ||
        !hexContractTo ||
        Number.isNaN(chainIdFrom) ||
        Number.isNaN(chainIdTo)
      ) {
        throw new Error('Cannot get intent info')
      }

      const sourceInfo = await this.getTokenInfo(chainIdFrom, hexContractFrom)
      const destinationInfo = await this.getTokenInfo(chainIdTo, hexContractTo)

      const amountFrom = fromMinorUnit(weiAmountFrom, sourceInfo.decimals)
      const amountFromUsd = amountFrom.times(sourceInfo.rate.rate).toString()
      const amountTo = fromMinorUnit(weiAmountTo, destinationInfo.decimals)
      const amountToUsd = amountTo.times(destinationInfo.rate.rate).toString()
      const fee = fromMinorUnit(tx.initial_data.feeAmount || 0, sourceInfo.decimals)
      const feeUsd = fee ? Big(fee).times(sourceInfo.rate.rate).toString() : '0'
      const explorerLink = getExplorerLinkForIntent(tx.intentId)

      switch (type) {
        case 'cross-chain-transfer':
        case 'local-transfer': {
          if (!tx.initial_data?.receiver_address) throw new Error('Cannot find recipient address')

          const addressTo = mapAddress(
            tx.initial_data.receiver_address,
            sourceInfo.chainInfo.chainNamespace === 'solana',
          )

          const sendTxInfo: SendTxInfo = {
            id: `intent-${tx.id}`,
            status,
            timestamp,
            addressTo,
            amountFrom: amountFrom.toString(),
            amountFromUsd,
            amountTo: amountTo?.toString(),
            amountToUsd,
            feeUsd,
            tokenIdFrom: sourceInfo.tokenInfo.id,
            chainIdFrom: sourceInfo.chainInfo.id,
            intentId: tx.intentId,
            tokenIdTo: sourceInfo.tokenInfo.id,
            chainIdTo: destinationInfo.chainInfo?.id,
            type: 'send',
            explorerLink,
          }
          txInfoSchema.parse(sendTxInfo)

          return sendTxInfo
        }

        case 'cross-chain-swap':
        case 'local-swap': {
          const swapType = mapSwapType(sourceInfo.tokenInfo.isStable, !!destinationInfo.tokenInfo?.isStable)

          if (!swapType || !amountTo || !amountToUsd) throw new Error('Cannot find destination info')

          const swapTxInfo: SwapTxInfo = {
            id: `intent-${tx.id}`,
            status,
            timestamp,
            amountFrom: amountFrom.toString(),
            amountFromUsd,
            amountTo: amountTo.toString(),
            amountToUsd,
            feeUsd,
            tokenIdFrom: sourceInfo.tokenInfo.id,
            chainIdFrom: sourceInfo.chainInfo.id,
            intentId: tx.intentId,
            tokenIdTo: destinationInfo.tokenInfo.id,
            chainIdTo: destinationInfo.chainInfo.id,
            type: swapType,
            explorerLink,
          }
          txInfoSchema.parse(swapTxInfo)

          return swapTxInfo
        }

        default:
          throw new Error('Unsupported intent type')
      }
    } catch (error) {
      const errorMessage = getErrorMessage(error)
      throw new Error(`${errorMessage}, intentId: ${tx.intentId}`)
    }
  }

  private async mapDepositToTxInfo(deposit: IndexerDeposit): Promise<DepositTxInfo> {
    try {
      const chainId = deposit.chain_id ? Number(deposit.chain_id) : undefined
      const { decimals, chainInfo, tokenInfo, rate } = await this.getTokenInfo(chainId, deposit.token_address)

      const amount = fromMinorUnit(deposit.amount, decimals)
      const amountUsd = amount.times(rate.rate).toString()
      let status: DepositTxInfo['status'] = 'success'
      const txHash = deposit.transaction_hash

      if (!txHash) throw new Error('Cannot find transaction hash')

      if (!deposit.message_id) throw new Error('Cannot find message id')

      if (typeof deposit.status === 'number') {
        status = deposit.status === 0 ? 'processing' : 'success'
      }

      const explorerLink = mapDepositLink(txHash, chainInfo)

      const depositTxInfo: DepositTxInfo = {
        id: `deposit-${deposit.id}`,
        status,
        txHash,
        type: 'deposit',
        timestamp: dayjs(deposit.timestamp).unix() * 1000,
        feeUsd: '0',
        amountFrom: amount.toString(),
        amountFromUsd: amountUsd,
        tokenIdFrom: tokenInfo.id,
        chainIdFrom: chainInfo.id,
        messageId: deposit.message_id,
        explorerLink,
      }
      depositTxInfoSchema.parse(depositTxInfo)

      return depositTxInfo
    } catch (e) {
      const errorMessage = getErrorMessage(e)
      throw new Error(`${errorMessage}, messageId: ${deposit.message_id}`)
    }
  }

  private async getTokenInfo(chainId?: number, contractAddress?: string) {
    if (!chainId) throw new Error('Chain id from is required')

    if (!contractAddress) throw new Error('Contract from is required')

    const chainInfo = await this.chainsService.getChainByChainId(chainId)

    if (!chainInfo) {
      throw new Error(`Cannot find chainInfoFrom. Provided id: ${chainId}`)
    }

    const contract = mapAddress(contractAddress, chainInfo.chainNamespace === 'solana')
    const tokenInfo = await this.tokensService.getTokenByContract(contract, chainId)

    if (!tokenInfo) throw new Error(`Cannot find token info for contract ${contract} on chain ${chainId}`)

    const rate = await this.tokensService.getRateById(tokenInfo.id)

    if (!rate) throw new Error(`Cannot find rate for token ${tokenInfo.id}`)

    const decimals = tokenInfo.chains.find((c) => c.id === chainInfo.id)?.decimals

    if (!decimals) {
      throw new Error(`Cannot find decimals for token ${tokenInfo.id} on chain ${chainInfo.id}`)
    }

    return { chainInfo, decimals, rate, tokenInfo }
  }
}
