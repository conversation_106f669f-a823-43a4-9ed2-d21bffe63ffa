import { z } from 'zod'

const intentOnChainSchema = z
  .object({
    chainId: z.string().nullable(),
    tokenIn: z.string().nullable(),
    tokenOut: z.string().nullable(),
    txHash: z.string().nullable(),
    explorerLink: z.string().nullable(),
    amountIn: z.string().nullable(),
    amountOut: z.string().nullable(),
    orderId: z.number().nullable(),
    order_payload: z.string().nullable(),
  })
  .nullable()

export const indexerIntentSchema = z.object({
  id: z.number(),
  intentId: z.number(),
  createdAt: z.string(),
  status: z.string(),
  isDeposit: z.boolean().nullable(),
  senderAddress: z.string().nullable(),
  solverAddress: z.string().nullable(),
  source: intentOnChainSchema,
  destination: intentOnChainSchema,
  initial_data: z.object({
    id: z.number(),
    intent_id: z.number(),
    origin_chain: z.string().nullable(),
    target_chain: z.string().nullable(),
    token_in: z.string().nullable(),
    amount_in: z.string().nullable(),
    token_out: z.string().nullable(),
    amount_out: z.string().nullable(),
    initiator_address: z.string().nullable(),
    solver_address: z.string().nullable(),
    ack_result: z.boolean().nullable(),
    ack_tx_status: z.string().nullable(),
    ack_error_message: z.string().nullable(),
    solver_tx_hash: z.string().nullable(),
    ack_tx_hash: z.string().nullable(),
    intent_version: z.number(),
    receiver_address: z.string().nullable(),
    feeAmount: z.string().nullable(),
  }),
  intent_type: z.string().nullable(),
})

export type IndexerIntent = z.infer<typeof indexerIntentSchema>
