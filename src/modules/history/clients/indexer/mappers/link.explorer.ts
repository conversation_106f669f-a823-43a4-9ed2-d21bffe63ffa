import { getEnvConfig } from '@/config/env'
import type { ChainInfo } from '@/modules/chains'

export const mapDepositLink = (txHash: string, chainInfo: ChainInfo): string | undefined => {
  const isHex = txHash.startsWith('0x')

  if (isHex && chainInfo.chainNamespace === 'solana') {
    const { l3Explorer } = getEnvConfig()

    return l3Explorer.txLinkTemplate.replace(l3Explorer.templatePlaceholder, txHash)
  }

  const linkTemplate = chainInfo?.blockExplorerUrls?.[0]

  if (!linkTemplate) return undefined

  return linkTemplate.replace('PARAM_TX_HASH', txHash)
}
