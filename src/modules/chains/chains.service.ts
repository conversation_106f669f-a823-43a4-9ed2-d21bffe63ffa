import { Injectable } from '@nestjs/common'

import { type ChainInfo } from './schemas/chain-info.schema'
import { ChainsStorage } from './storage'

@Injectable()
export class ChainsService {
  constructor(private storage: ChainsStorage) {}

  async getChains(): Promise<ChainInfo[]> {
    return this.storage.getChains()
  }

  async getChainById(id: string): Promise<ChainInfo | undefined> {
    return this.storage.getChainById(id)
  }

  async getChainByChainId(id: number): Promise<ChainInfo | undefined> {
    return this.storage.getChainByChainId(id)
  }
}
