import { sql } from 'drizzle-orm'
import {
  pgTable,
  unique,
  bigserial,
  text,
  timestamp,
  integer,
  bigint,
  varchar,
  serial,
  boolean,
  foreignKey,
  numeric,
  jsonb,
  pgEnum,
} from 'drizzle-orm/pg-core'

export const poolTypeEnum = pgEnum('pool_type_enum', ['EVM', 'SOLANA'])
export const tokenLaunchType = pgEnum('token_launch_type', ['FAIR', 'CURATED'])

export const depositReceived = pgTable(
  'deposit_received',
  {
    id: bigserial({ mode: 'bigint' }).primaryKey().notNull(),
    userAddress: text('user_address').notNull(),
    tokenAddress: text('token_address').notNull(),
    chainId: text('chain_id').notNull(),
    amount: text().notNull(),
    timestamp: timestamp({ mode: 'string' }).notNull(),
    sourceTransactionHash: text('source_transaction_hash'),
    messageId: text('message_id'),
    status: integer(),
  },
  (table) => [
    unique('deposit_received_message_id_key').on(table.messageId),
    unique('deposit_received_unique_message_id').on(table.messageId),
  ],
)

export const messageDispatchedFromVault = pgTable(
  'message_dispatched_from_vault',
  {
    id: bigserial({ mode: 'bigint' }).primaryKey().notNull(),
    // You can use { mode: "bigint" } if numbers are exceeding js number limitations
    intentId: bigint('intent_id', { mode: 'number' }).notNull(),
    senderAddress: varchar('sender_address', { length: 66 }).notNull(),
    destinationDomainId: integer('destination_domain_id').notNull(),
    provider: integer().notNull(),
    message: text().notNull(),
    transactionHash: varchar('transaction_hash', { length: 88 }).notNull(),
    // You can use { mode: "bigint" } if numbers are exceeding js number limitations
    blockNumber: bigint('block_number', { mode: 'number' }).notNull(),
    timestamp: timestamp({ mode: 'string' }).notNull(),
    // You can use { mode: "bigint" } if numbers are exceeding js number limitations
    orderId: bigint('order_id', { mode: 'number' }).notNull(),
  },
  (table) => [unique('message_dispatched_from_vault_unique_transaction_hash').on(table.transactionHash)],
)

export const receivedMessageOnVault = pgTable(
  'received_message_on_vault',
  {
    id: bigserial({ mode: 'bigint' }).primaryKey().notNull(),
    // You can use { mode: "bigint" } if numbers are exceeding js number limitations
    intentId: bigint('intent_id', { mode: 'number' }).notNull(),
    originDomainId: integer('origin_domain_id').notNull(),
    senderAddress: varchar('sender_address', { length: 44 }).notNull(),
    message: text().notNull(),
    provider: integer().notNull(),
    transactionHash: varchar('transaction_hash', { length: 88 }).notNull(),
    // You can use { mode: "bigint" } if numbers are exceeding js number limitations
    blockNumber: bigint('block_number', { mode: 'number' }).notNull(),
    timestamp: timestamp({ mode: 'string' }).notNull(),
    // You can use { mode: "bigint" } if numbers are exceeding js number limitations
    chainId: bigint('chain_id', { mode: 'number' }).notNull(),
    // You can use { mode: "bigint" } if numbers are exceeding js number limitations
    orderId: bigint('order_id', { mode: 'number' }).notNull(),
    dlnOrderId: varchar('dln_order_id', { length: 255 }),
    // You can use { mode: "bigint" } if numbers are exceeding js number limitations
    timeoutUnixTimestampInSec: bigint('timeout_unix_timestamp_in_sec', { mode: 'number' }).default(0),
  },
  (table) => [unique('received_message_on_vault_unique_transaction_hash').on(table.transactionHash)],
)

export const sanctionAddressList = pgTable(
  'sanction_address_list',
  {
    id: serial().primaryKey().notNull(),
    address: text().notNull(),
  },
  (table) => [unique('sanction_address_list_address_key').on(table.address)],
)

export const solution = pgTable(
  'solution',
  {
    id: bigserial({ mode: 'bigint' }).primaryKey().notNull(),
    // You can use { mode: "bigint" } if numbers are exceeding js number limitations
    intentId: bigint('intent_id', { mode: 'number' }).notNull(),
    solverAddress: varchar('solver_address', { length: 44 }).notNull(),
    transactionHash: varchar('transaction_hash', { length: 88 }).notNull(),
    // You can use { mode: "bigint" } if numbers are exceeding js number limitations
    blockNumber: bigint('block_number', { mode: 'number' }).notNull(),
    timestamp: timestamp({ mode: 'string' }).notNull(),
  },
  (table) => [unique('solution_unique_transaction_hash').on(table.transactionHash)],
)

export const orderCreated = pgTable(
  'order_created',
  {
    id: bigserial({ mode: 'bigint' }).primaryKey().notNull(),
    // You can use { mode: "bigint" } if numbers are exceeding js number limitations
    intentId: bigint('intent_id', { mode: 'number' }).notNull(),
    creatorAddress: varchar('creator_address', { length: 66 }).notNull(),
    tokenIn: varchar('token_in', { length: 66 }).notNull(),
    tokenOut: varchar('token_out', { length: 66 }).notNull(),
    amountIn: text('amount_in').notNull(),
    amountOut: text('amount_out').notNull(),
    transactionHash: varchar('transaction_hash', { length: 88 }).notNull(),
    // You can use { mode: "bigint" } if numbers are exceeding js number limitations
    blockNumber: bigint('block_number', { mode: 'number' }).notNull(),
    timestamp: timestamp({ mode: 'string' }).notNull(),
    // You can use { mode: "bigint" } if numbers are exceeding js number limitations
    orderId: bigint('order_id', { mode: 'number' }).notNull(),
    sourceChainId: text('source_chain_id').notNull(),
    destinationChainId: text('destination_chain_id').notNull(),
    multiLeg: boolean('multi_leg').default(false).notNull(),
    orderPayload: text('order_payload').default('').notNull(),
    solutionType: integer('solution_type'),
    receiverType: integer('receiver_type').default(0).notNull(),
    receiverAddress: text('receiver_address').default(''),
    amountInUsd: text('amount_in_usd').default('0'),
    amountOutUsd: text('amount_out_usd').default('0'),
  },
  (table) => [unique('order_created_unique_transaction_hash').on(table.transactionHash)],
)

export const tokens = pgTable('tokens', {
  id: text().primaryKey().notNull(),
  ticker: text(),
  fullName: text('full_name'),
  isStable: boolean('is_stable'),
  isTradable: boolean('is_tradable'),
  priceUsd: numeric('price_usd'),
  description: text(),
  launchDate: timestamp('launch_date', { mode: 'string' }),
  website: text(),
  cmcId: text('cmc_id'),
})

export const tokenChains = pgTable(
  'token_chains',
  {
    id: serial().primaryKey().notNull(),
    tokenId: text('token_id'),
    address: text(),
    decimals: integer(),
    network: text(),
    addressBytes32: text('address_bytes32'),
  },
  (table) => [
    foreignKey({
      columns: [table.tokenId],
      foreignColumns: [tokens.id],
      name: 'token_chains_token_id_fkey',
    }).onDelete('cascade'),
  ],
)

export const chainMetadata = pgTable(
  'chain_metadata',
  {
    chainId: text('chain_id').primaryKey().notNull(),
    networkName: text('network_name').notNull(),
  },
  (table) => [unique('chain_metadata_network_name_key').on(table.networkName)],
)

export const acknowledgement = pgTable(
  'acknowledgement',
  {
    id: bigserial({ mode: 'bigint' }).primaryKey().notNull(),
    // You can use { mode: "bigint" } if numbers are exceeding js number limitations
    intentId: bigint('intent_id', { mode: 'number' }).notNull(),
    senderAddress: varchar('sender_address', { length: 44 }).notNull(),
    result: boolean().notNull(),
    errorMessage: text('error_message'),
    transactionHash: varchar('transaction_hash', { length: 88 }).notNull(),
    // You can use { mode: "bigint" } if numbers are exceeding js number limitations
    blockNumber: bigint('block_number', { mode: 'number' }).notNull(),
    timestamp: timestamp({ mode: 'string' }).notNull(),
    // You can use { mode: "bigint" } if numbers are exceeding js number limitations
    orderId: bigint('order_id', { mode: 'number' }).notNull(),
    metadata: text().default(''),
  },
  (table) => [unique('acknowledgement_unique_transaction_hash').on(table.transactionHash)],
)

export const intent = pgTable(
  'intent',
  {
    id: bigserial({ mode: 'bigint' }).primaryKey().notNull(),
    // You can use { mode: "bigint" } if numbers are exceeding js number limitations
    intentId: bigint('intent_id', { mode: 'number' }).notNull(),
    ownerAddress: varchar('owner_address', { length: 66 }).notNull(),
    transactionHash: varchar('transaction_hash', { length: 88 }).notNull(),
    // You can use { mode: "bigint" } if numbers are exceeding js number limitations
    blockNumber: bigint('block_number', { mode: 'number' }).notNull(),
    timestamp: timestamp({ mode: 'string' }).notNull(),
    feeamount: text().default(''),
  },
  (table) => [unique('intent_unique_transaction_hash').on(table.transactionHash)],
)

export const intentFees = pgTable(
  'intent_fees',
  {
    id: serial().primaryKey().notNull(),
    // You can use { mode: "bigint" } if numbers are exceeding js number limitations
    intentId: bigint('intent_id', { mode: 'number' }).notNull(),
    fees: jsonb().notNull(),
  },
  (table) => [
    unique('intent_fees_intent_id_key').on(table.intentId),
    unique('intent_fees_unique_intent_id').on(table.intentId),
  ],
)

export const intentState = pgTable(
  'intent_state',
  {
    id: bigserial({ mode: 'bigint' }).primaryKey().notNull(),
    // You can use { mode: "bigint" } if numbers are exceeding js number limitations
    intentId: bigint('intent_id', { mode: 'number' }).notNull(),
    version: integer().notNull(),
    transactionHash: varchar('transaction_hash', { length: 88 }).notNull(),
    stage: text().notNull(),
    timestamp: timestamp({ mode: 'string' }).notNull(),
    // You can use { mode: "bigint" } if numbers are exceeding js number limitations
    gasFees: bigint('gas_fees', { mode: 'number' }),
    gasToken: text('gas_token'),
    // You can use { mode: "bigint" } if numbers are exceeding js number limitations
    orderId: bigint('order_id', { mode: 'number' }),
    // You can use { mode: "bigint" } if numbers are exceeding js number limitations
    chainId: bigint('chain_id', { mode: 'number' }).notNull(),
    initiatorAddress: varchar('initiator_address', { length: 66 }).default('0x').notNull(),
    transactionCost: text('transaction_cost').default('0'),
    transactionCostUsd: text('transaction_cost_usd').default('0'),
  },
  (table) => [unique('unique_intent_version_tx').on(table.intentId, table.version, table.transactionHash)],
)

export const dieselSchemaMigrations = pgTable('__diesel_schema_migrations', {
  version: varchar({ length: 50 }).primaryKey().notNull(),
  runOn: timestamp('run_on', { mode: 'string' })
    .default(sql`CURRENT_TIMESTAMP`)
    .notNull(),
})

export const liquidity = pgTable(
  'liquidity',
  {
    id: bigserial({ mode: 'bigint' }).primaryKey().notNull(),
    poolAddress: text('pool_address').notNull(),
    userAddress: text('user_address').notNull(),
    isAdd: boolean('is_add').notNull(),
    positionId: text('position_id').notNull(),
    token0Amount: numeric('token_0_amount').notNull(),
    token1Amount: numeric('token_1_amount').notNull(),
    // You can use { mode: "bigint" } if numbers are exceeding js number limitations
    chainId: bigint('chain_id', { mode: 'number' }).notNull(),
    timestamp: timestamp({ mode: 'string' }).notNull(),
    transactionHash: text('transaction_hash').notNull(),
    isManager: boolean('is_manager').notNull(),
    // You can use { mode: "bigint" } if numbers are exceeding js number limitations
    liquidity: bigint({ mode: 'number' }).notNull(),
    isVault: boolean('is_vault').notNull(),
  },
  (table) => [unique('liquidity_unique_transaction_hash').on(table.transactionHash)],
)

export const ohlcPriceTables = pgTable(
  'ohlc_price_tables',
  {
    id: bigserial({ mode: 'bigint' }).primaryKey().notNull(),
    tokenAddress: text('token_address').notNull(),
    // You can use { mode: "bigint" } if numbers are exceeding js number limitations
    chainId: bigint('chain_id', { mode: 'number' }).notNull(),
    interval: text().notNull(),
    openPrice: numeric('open_price').notNull(),
    highPrice: numeric('high_price').notNull(),
    lowPrice: numeric('low_price').notNull(),
    closePrice: numeric('close_price').notNull(),
    volumeToken: numeric('volume_token').notNull(),
    volumeUsd: numeric('volume_usd'),
    timestampBucket: timestamp('timestamp_bucket', { mode: 'string' }).notNull(),
    poolAddress: text('pool_address').notNull(),
  },
  (table) => [
    unique('ohlc_unique').on(
      table.tokenAddress,
      table.chainId,
      table.interval,
      table.timestampBucket,
      table.poolAddress,
    ),
  ],
)

export const ammswap = pgTable(
  'ammswap',
  {
    id: bigserial({ mode: 'bigint' }).primaryKey().notNull(),
    poolAddress: text('pool_address').notNull(),
    tokenIn: text('token_in').notNull(),
    tokenOut: text('token_out').notNull(),
    amountIn: numeric('amount_in').notNull(),
    amountOut: numeric('amount_out').notNull(),
    // You can use { mode: "bigint" } if numbers are exceeding js number limitations
    chainId: bigint('chain_id', { mode: 'number' }).notNull(),
    amountInUsd: numeric('amount_in_usd').notNull(),
    amountOutUsd: numeric('amount_out_usd').notNull(),
    initiatorUserAddress: text('initiator_user_address').notNull(),
    price: numeric().notNull(),
    timestamp: timestamp({ mode: 'string' }).notNull(),
    transactionHash: text('transaction_hash').notNull(),
    // You can use { mode: "bigint" } if numbers are exceeding js number limitations
    blockNumber: bigint('block_number', { mode: 'number' }).notNull(),
    isVaultInitiated: boolean('is_vault_initiated').notNull(),
    sqrtPrice: text('sqrt_price').notNull(),
    // You can use { mode: "bigint" } if numbers are exceeding js number limitations
    liquidity: bigint({ mode: 'number' }).notNull(),
    tick: integer().notNull(),
  },
  (table) => [unique('ammswap_unique_transaction_hash').on(table.transactionHash)],
)

export const pools = pgTable(
  'pools',
  {
    id: bigserial({ mode: 'bigint' }).primaryKey().notNull(),
    poolAddress: text('pool_address').notNull(),
    // You can use { mode: "bigint" } if numbers are exceeding js number limitations
    chainId: bigint('chain_id', { mode: 'number' }).notNull(),
    token0Address: text('token_0_address').notNull(),
    token1Address: text('token_1_address').notNull(),
    fee: numeric().notNull(),
    // You can use { mode: "bigint" } if numbers are exceeding js number limitations
    tickSpacing: bigint('tick_spacing', { mode: 'number' }).notNull(),
    poolType: poolTypeEnum('pool_type').notNull(),
    projectManager: text('project_manager').notNull(),
    // You can use { mode: "bigint" } if numbers are exceeding js number limitations
    blockNumber: bigint('block_number', { mode: 'number' }).notNull(),
    createdAt: timestamp('created_at', { mode: 'string' }).notNull(),
    metadata: jsonb(),
    etpStartTime: timestamp('etp_start_time', { mode: 'string' }).notNull(),
    etpEndTime: timestamp('etp_end_time', { mode: 'string' }).notNull(),
    launchType: tokenLaunchType('launch_type').notNull(),
    initialSqrtPrice: text('initial_sqrt_price').notNull(),
    initialTick: integer('initial_tick').notNull(),
    tokenSupply: text('token_supply').notNull(),
    launchpadToken: text('launchpad_token').default('').notNull(),
  },
  (table) => [unique('pools_pool_address_key').on(table.poolAddress)],
)
