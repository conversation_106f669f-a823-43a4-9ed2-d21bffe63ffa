# Indexer API Migration

This document outlines the migration from direct database connections to the indexer database to using HTTP API calls to the indexer service.

## Overview

The codebase has been refactored to remove all direct database connections to the indexer database through Drizzle ORM and replace them with HTTP requests to the indexer API. This change moves the architecture towards a service-oriented approach where the indexer provides a clean API interface.

## Changes Made

### 1. Created IndexerApiClient

**File**: `src/modules/pool/clients/indexer-api.client.ts`

A new HTTP client service that provides methods to interact with the indexer API:

- `getTotalPoolLiquidity(poolAddress, options?)` - Get total liquidity for a specific pool
- `getTotalPmLiquidity()` - Get total Project Manager liquidity across all pools  
- `getTokenDetails(tokenAddress, chainId)` - Get token details by address and chain ID
- `hasPoolLiquidity(poolAddress)` - Check if a pool has any liquidity
- `hasPoolPmLiquidity(poolAddress)` - Check if a pool has PM liquidity

### 2. API Endpoints Supported

The client integrates with these indexer API endpoints:

- **GET /total_pool_liquidity/{pool_address}** - Returns string representation of total liquidity
  - Optional query parameter: `pm_liquidity_only` (boolean, defaults to false)
  - When `pm_liquidity_only=true`, returns only PM liquidity for that specific pool
- **GET /total_pm_liquidity** - Returns string representation of total PM liquidity across all pools
- **GET /tokens/{token_address}/{chain_id}** - Returns token details object

### 3. Enhanced Error Handling

The client includes comprehensive error handling with custom error types:

- `IndexerApiError` - For HTTP errors from the API
- `IndexerApiNetworkError` - For network/connection issues
- `IndexerApiValidationError` - For response validation failures

Error handling includes:
- Proper classification of different error types
- Detailed logging with context
- Safe fallback behavior for boolean checks
- Timeout configuration (10 seconds)

### 4. Updated PoolService

**File**: `src/modules/pool/pool.service.ts`

The `checkLiquidity` method has been refactored to:
- Remove direct indexer database queries
- Use the new `IndexerApiClient` instead
- Maintain the same public interface and behavior
- Provide better error handling and logging

### 5. Module Dependencies Updated

**File**: `src/modules/pool/pool.module.ts`

- Removed `IndexerDatabaseModule` import
- Added `IndexerApiClient` as a provider
- Updated constructor injection in `PoolService`

### 6. Removed Indexer Database Components

The following files and configurations were removed:
- `src/libs/indexer-database/` directory (entire module)
- Indexer database schema definitions
- Indexer database connection configuration
- `INDEXER_DATABASE_URL` environment variable usage

### 7. Configuration Updates

**File**: `src/config/env.ts`
- Removed `indexerDatabase` configuration object
- Kept `indexer.apiUrl` configuration (already existed)

**File**: `drizzle.config.ts`
- Updated to point to main database schema instead of indexer schema

## Environment Variables

### Required
- `INDEXER_API_URL` - URL of the indexer API service (already configured)

### No Longer Used
- `INDEXER_DATABASE_URL` - Direct database connection (removed)

## Benefits

1. **Service-Oriented Architecture**: Clean separation between services
2. **Reduced Coupling**: No direct database dependencies on indexer
3. **Better Error Handling**: Comprehensive error classification and handling
4. **Improved Maintainability**: Single API interface instead of database schema dependencies
5. **Network Resilience**: Proper timeout and retry handling
6. **Logging**: Enhanced logging for debugging and monitoring

## Backward Compatibility

The public interface of the `PoolService.checkLiquidity` method remains unchanged. All existing API endpoints and responses maintain the same structure, ensuring no breaking changes for consumers.

## Testing Considerations

When writing tests for the new implementation:

1. **Mock the IndexerApiClient** instead of database connections
2. **Test error scenarios** including network failures and API errors
3. **Verify fallback behavior** for boolean check methods
4. **Test timeout handling** and retry logic
5. **Validate response parsing** and schema validation

## Monitoring

Monitor the following metrics:
- API response times to indexer service
- Error rates by error type (network, API, validation)
- Fallback behavior frequency
- Pool liquidity check success rates

## Future Enhancements

Potential improvements for the future:
- Implement caching for frequently accessed data
- Add retry logic with exponential backoff
- Implement circuit breaker pattern for resilience
- Add metrics collection for monitoring
- Consider implementing batch operations for multiple pools
