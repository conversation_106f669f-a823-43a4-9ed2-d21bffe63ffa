### Variables
@baseUrl = http://localhost:3001
@contentType = application/json

### Authentication Variables (copy from auth.http when needed)
@sessionToken = XXXXXXX
@sessionData = XXXXXXXXX

### Test variables (for easy updates)
@testProjectId = REPLACE_WITH_ACTUAL_PROJECT_ID

###############################################
### PROJECT CREATION & MANAGEMENT
###############################################

### Create a New Project
POST {{baseUrl}}/projects
Cookie: __Secure-caishen-pro.session_token={{sessionToken}}; __Secure-caishen-pro.session_data={{sessionData}}
Content-Type: {{contentType}}

{
  "projectName": "DeFi Revolution Token",
  "projectWebsite": "https://defi-revolution.com",
  "contactName": "<PERSON>",
  "contactEmail": "<EMAIL>",
  "roleInProject": "Founder & CEO",
  "blockchainToLaunchOn": "solana",
  "teamPublicProfileLinks": "https://linkedin.com/in/johndoe, https://github.com/johndoe"
}

### Get All User Projects
GET {{baseUrl}}/projects
Cookie: __Secure-caishen-pro.session_token={{sessionToken}}; __Secure-caishen-pro.session_data={{sessionData}}

### Get Specific Project by ID
GET {{baseUrl}}/projects/{{testProjectId}}
Cookie: __Secure-caishen-pro.session_token={{sessionToken}}; __Secure-caishen-pro.session_data={{sessionData}}

###############################################
### PROJECT UPDATES
###############################################

### Update Project (Basic Info)
PUT {{baseUrl}}/projects/{{testProjectId}}
Cookie: __Secure-caishen-pro.session_token={{sessionToken}}; __Secure-caishen-pro.session_data={{sessionData}}
Content-Type: {{contentType}}

{
  "projectName": "DeFi Revolution Token - Updated",
  "projectWebsite": "https://new-defi-revolution.com",
  "currentFormStep": 2
}

### Update Project (Token Details - Step 8)
PUT {{baseUrl}}/projects/{{testProjectId}}
Cookie: __Secure-caishen-pro.session_token={{sessionToken}}; __Secure-caishen-pro.session_data={{sessionData}}
Content-Type: {{contentType}}

{
  "tokenName": "DeFi Revolution Token",
  "tokenSymbol": "DEFIR",
  "tokenContractAddress": "0x1234567890abcdef1234567890abcdef12345678",
  "totalTokenSupply": "1000000000",
  "tokenLaunchMarketCapUsd": "5000000.00",
  "tokenThumbnailImageUrl": "https://example.com/token-thumbnail.png",
  "currentFormStep": 8
}

### Update Project (TGE Details - Step 9)
PUT {{baseUrl}}/projects/{{testProjectId}}
Cookie: __Secure-caishen-pro.session_token={{sessionToken}}; __Secure-caishen-pro.session_data={{sessionData}}
Content-Type: {{contentType}}

{
  "tgeType": "fixed_price",
  "allocationTotalSupplyCaishen": "10.00",
  "allocationCirculatingSupplyCaishen": "5.00",
  "acceptedCurrenciesForPairing": "SOL, USDC",
  "tgePriceSol": "0.001",
  "exclusiveTradingPeriodHours": 24,
  "expectedTgeLaunchDate": "2025-07-01",
  "liquidityActivationDate": "2025-07-02",
  "minAmountPerTradeSol": "0.1",
  "maxAmountPerTradeSol": "100.0",
  "currentFormStep": 9
}

### Update Project (Social Media - Step 5)
PUT {{baseUrl}}/projects/{{testProjectId}}
Cookie: __Secure-caishen-pro.session_token={{sessionToken}}; __Secure-caishen-pro.session_data={{sessionData}}
Content-Type: {{contentType}}

{
  "officialTwitter": "https://twitter.com/defirevolution",
  "discordUrl": "https://discord.gg/defirevolution",
  "telegram": "https://t.me/defirevolution",
  "farcaster": "defirevolution",
  "currentFormStep": 5
}

###############################################
### PROJECT LIFECYCLE
###############################################

### Submit Project for Review
POST {{baseUrl}}/projects/{{testProjectId}}/submit
Cookie: __Secure-caishen-pro.session_token={{sessionToken}}; __Secure-caishen-pro.session_data={{sessionData}}
Content-Type: {{contentType}}

### Delete Project (Draft Only)
DELETE {{baseUrl}}/projects/{{testProjectId}}
Cookie: __Secure-caishen-pro.session_token={{sessionToken}}; __Secure-caishen-pro.session_data={{sessionData}}

###############################################
### TESTING EXAMPLES WITH EDITOR.JS FORMAT
###############################################

### Update Project (Rich Text Fields)
PUT {{baseUrl}}/projects/{{testProjectId}}
Cookie: __Secure-caishen-pro.session_token={{sessionToken}}; __Secure-caishen-pro.session_data={{sessionData}}
Content-Type: {{contentType}}

{
  "projectDetails": {
    "time": 1625097600000,
    "blocks": [
      {
        "type": "paragraph",
        "data": {
          "text": "DeFi Revolution is a next-generation decentralized finance protocol that aims to democratize access to financial services."
        }
      },
      {
        "type": "header",
        "data": {
          "text": "Key Features",
          "level": 2
        }
      },
      {
        "type": "list",
        "data": {
          "style": "unordered",
          "items": [
            "Cross-chain liquidity pools",
            "Automated market making",
            "Yield farming opportunities",
            "Governance token distribution"
          ]
        }
      }
    ],
    "version": "2.22.2"
  },
  "tokenomicsDetails": {
    "time": 1625097600000,
    "blocks": [
      {
        "type": "header",
        "data": {
          "text": "Token Distribution",
          "level": 2
        }
      },
      {
        "type": "paragraph",
        "data": {
          "text": "Total Supply: 1,000,000,000 DEFIR tokens"
        }
      },
      {
        "type": "list",
        "data": {
          "style": "unordered",
          "items": [
            "30% - Public Sale",
            "25% - Team & Advisors (2-year vesting)",
            "20% - Ecosystem & Partnerships",
            "15% - Liquidity Pool",
            "10% - Marketing & Development"
          ]
        }
      }
    ],
    "version": "2.22.2"
  },
  "currentFormStep": 6
}

###############################################
### QUICK TEST WORKFLOW
###############################################

### Step 1: Create a minimal project
# POST {{baseUrl}}/projects
# Cookie: __Secure-caishen-pro.session_token={{sessionToken}}; __Secure-caishen-pro.session_data={{sessionData}}
# Content-Type: {{contentType}}
#
# {
#   "projectName": "Test Project",
#   "projectWebsite": "https://test-project.com",
#   "contactName": "Test User",
#   "contactEmail": "<EMAIL>",
#   "roleInProject": "Founder",
#   "blockchainToLaunchOn": "ethereum"
# }

### Step 2: Copy the returned project ID and replace @testProjectId above

### Step 3: Get all your projects to verify creation
# GET {{baseUrl}}/projects
# Cookie: __Secure-caishen-pro.session_token={{sessionToken}}; __Secure-caishen-pro.session_data={{sessionData}}

### Step 4: Update the project with additional details
# PUT {{baseUrl}}/projects/{{testProjectId}}
# Cookie: __Secure-caishen-pro.session_token={{sessionToken}}; __Secure-caishen-pro.session_data={{sessionData}}
# Content-Type: {{contentType}}
#
# {
#   "tokenName": "Test Token",
#   "tokenSymbol": "TEST",
#   "currentFormStep": 3
# }

### Step 5: Get the specific project to see updates
# GET {{baseUrl}}/projects/{{testProjectId}}
# Cookie: __Secure-caishen-pro.session_token={{sessionToken}}; __Secure-caishen-pro.session_data={{sessionData}}

###############################################
### NOTES
###############################################
# 1. Replace @testProjectId with actual project ID after creation
# 2. Update @sessionToken and @sessionData from your auth.http file
# 3. Projects require authentication via session cookies
# 4. Only draft projects can be deleted
# 5. Projects must be complete (all 11 steps) before submission
# 6. Rich text fields use Editor.js format
# 7. Available blockchains: ethereum, solana, polygon, bsc, arbitrum, base, optimism
# 8. TGE types: fixed_price, auction, bonding_curve 