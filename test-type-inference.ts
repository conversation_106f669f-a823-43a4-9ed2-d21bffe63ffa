// Test file to demonstrate the type inference improvement
import { submissionStatusEnum } from './src/libs/database/schema'

// This is the old way - hardcoded string literals
type OldSubmissionStatus =
  | 'draft'
  | 'submitted'
  | 'under_review'
  | 'needs_revision'
  | 'approved'
  | 'wallet_provided'
  | 'fee_paid'
  | 'liquidity_provided'

// This is the new way - inferred from database schema
type NewSubmissionStatus = (typeof submissionStatusEnum.enumValues)[number]

// Test function to show type safety
function testOldWay(status: OldSubmissionStatus) {
  console.log(`Old way status: ${status}`)
}

function testNewWay(status: NewSubmissionStatus) {
  console.log(`New way status: ${status}`)
}

// These should work with both approaches
testOldWay('draft')
testNewWay('draft')

// If we add a new status to the database schema, only the new way will automatically include it
// The old way would need manual updates to the type definition

// Demonstrate that the types are equivalent
const oldStatus: OldSubmissionStatus = 'approved'
const newStatus: NewSubmissionStatus = oldStatus // This should work

console.log('Type inference test completed successfully!')
